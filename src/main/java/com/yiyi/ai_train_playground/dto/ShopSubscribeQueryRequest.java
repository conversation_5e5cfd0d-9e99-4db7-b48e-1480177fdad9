package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 店铺订购查询请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "店铺订购查询请求")
public class ShopSubscribeQueryRequest {
    
    @Schema(description = "店铺ID", example = "12345")
    private Long shopId;
    
    @Schema(description = "店铺类型,0:京东，1：淘宝，2：抖店，3: 网盟入口", example = "0")
    private Integer shopType;
    
    @Schema(description = "订购状态（1：订购、2：续费、3：升级）", example = "1")
    private Integer status;
    
    @Schema(description = "订单状态（1：等待付款、2：等待付款确认、4：订单完成、8：取消完成、16：退款中、64：退款完成）", example = "4")
    private Integer orderStatus;
    
    @Schema(description = "买家pin", example = "buyer123")
    private String buyer;
    
    @Schema(description = "服务编码", example = "JD001")
    private String itemCode;
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * pageSize;
    }
}
