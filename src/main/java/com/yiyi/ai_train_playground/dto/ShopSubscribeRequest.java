package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 店铺订购请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "店铺订购请求")
public class ShopSubscribeRequest {
    
    @Schema(description = "店铺ID", required = true, example = "12345")
    @NotNull(message = "店铺ID不能为空")
    private Long shopId;
    
    @Schema(description = "店铺类型,0:京东，1：淘宝，2：抖店，3: 网盟入口", required = true, example = "0")
    @NotNull(message = "店铺类型不能为空")
    private Integer shopType;
    
    @Schema(description = "购买时间", example = "2025-01-17T10:00:00")
    private LocalDateTime startTime;
    
    @Schema(description = "到期时间", example = "2025-07-17T10:00:00")
    private LocalDateTime endTime;
    
    @Schema(description = "订购状态（1：订购、2：续费、3：升级）", example = "1")
    private Integer status;
    
    @Schema(description = "订购版本", example = "不限账号")
    private String version;
    
    @Schema(description = "订购期限（天数）", example = "180")
    private Integer orderCycle;
    
    @Schema(description = "买家pin", example = "buyer123")
    private String buyer;
    
    @Schema(description = "买家昵称", example = "买家昵称")
    private String nickName;
    
    @Schema(description = "服务名称", example = "京东服务")
    private String serviceName;
    
    @Schema(description = "服务编码", example = "JD001")
    private String itemCode;
    
    @Schema(description = "订单状态（1：等待付款、2：等待付款确认、4：订单完成、8：取消完成、16：退款中、64：退款完成）", example = "4")
    private Integer orderStatus;
}
