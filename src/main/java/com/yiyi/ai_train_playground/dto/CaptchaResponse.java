package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图形验证码响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-09-09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "图形验证码响应")
public class CaptchaResponse {
    
    @Schema(description = "验证码key，用于后续验证", example = "captcha:uuid123456")
    private String captchaKey;
    
    @Schema(description = "验证码图片base64编码", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    private String captchaImage;
}