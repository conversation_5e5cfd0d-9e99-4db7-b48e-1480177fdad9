package com.yiyi.ai_train_playground.controller.external;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ShopSubscribeQueryRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeResponse;
import com.yiyi.ai_train_playground.service.ShopSubscribeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 外部程序专用店铺订购控制器
 * 提供给外部系统调用的API接口
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/external/api/shop-subscribe")
@RequiredArgsConstructor
@Tag(name = "外部店铺订购管理", description = "供外部程序调用的店铺订购相关接口")
@CrossOrigin(origins = "*") // 允许跨域访问
public class ExternalShopSubscribeController {

    private final ShopSubscribeService shopSubscribeService;

    /**
     * 创建店铺订购记录
     */
    @PostMapping
    @Operation(summary = "创建店铺订购记录", description = "创建新的店铺订购记录")
    public Result<String> create(
            @Parameter(description = "API密钥", required = true)
            @RequestHeader(value = "X-API-KEY", required = false) String apiKey,
            @Valid @RequestBody ShopSubscribeRequest request) {
        
        log.info("[EXTERNAL] 收到创建店铺订购记录请求: shopId={}, shopType={}, apiKey={}", 
                request.getShopId(), request.getShopType(), apiKey != null ? "***" : "null");
        
        // 这里可以添加API密钥验证逻辑
        if (!validateApiKey(apiKey)) {
            log.warn("[EXTERNAL] API密钥验证失败: apiKey={}", apiKey != null ? "***" : "null");
            return Result.error(401, "API密钥无效");
        }
        
        try {
            boolean success = shopSubscribeService.create(request);
            
            if (success) {
                log.info("[EXTERNAL] 创建店铺订购记录成功: shopId={}", request.getShopId());
                return Result.success("创建成功");
            } else {
                log.warn("[EXTERNAL] 创建店铺订购记录失败: shopId={}", request.getShopId());
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("[EXTERNAL] 创建店铺订购记录失败: shopId={}", request.getShopId(), e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据店铺ID查询有效的订购记录
     */
    @GetMapping("/valid/shop/{shopId}")
    @Operation(summary = "根据店铺ID查询有效的订购记录", description = "根据店铺ID查询该店铺的有效订购记录")
    public Result<List<ShopSubscribeResponse>> findValidSubscriptionsByShopId(
            @Parameter(description = "API密钥", required = true)
            @RequestHeader(value = "X-API-KEY", required = false) String apiKey,
            @Parameter(description = "店铺ID", required = true, example = "12345")
            @PathVariable Long shopId) {
        
        log.info("[EXTERNAL] 收到根据店铺ID查询有效订购记录请求: shopId={}", shopId);
        
        if (!validateApiKey(apiKey)) {
            return Result.error(401, "API密钥无效");
        }
        
        try {
            if (shopId == null) {
                return Result.error("店铺ID不能为空");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptionsByShopId(shopId);
            
            log.info("[EXTERNAL] 根据店铺ID查询有效订购记录成功: shopId={}, 数量={}", shopId, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[EXTERNAL] 根据店铺ID查询有效订购记录失败: shopId={}", shopId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询有效的订购记录（未过期）
     */
    @GetMapping("/valid")
    @Operation(summary = "查询有效的订购记录", description = "查询所有未过期的订购记录")
    public Result<List<ShopSubscribeResponse>> findValidSubscriptions(
            @Parameter(description = "API密钥", required = true)
            @RequestHeader(value = "X-API-KEY", required = false) String apiKey) {
        
        log.info("[EXTERNAL] 收到查询有效订购记录请求");
        
        if (!validateApiKey(apiKey)) {
            return Result.error(401, "API密钥无效");
        }
        
        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptions();
            
            log.info("[EXTERNAL] 查询有效订购记录成功: 数量={}", responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[EXTERNAL] 查询有效订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询店铺订购记录
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询店铺订购记录", description = "根据条件分页查询店铺订购记录")
    public Result<PageResult<ShopSubscribeResponse>> findWithPagination(
            @Parameter(description = "API密钥", required = true)
            @RequestHeader(value = "X-API-KEY", required = false) String apiKey,
            @RequestBody ShopSubscribeQueryRequest request) {
        
        log.info("[EXTERNAL] 收到分页查询店铺订购记录请求: page={}, pageSize={}", 
                request.getPage(), request.getPageSize());
        
        if (!validateApiKey(apiKey)) {
            return Result.error(401, "API密钥无效");
        }
        
        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findWithPagination(request);
            Long total = shopSubscribeService.countByConditions(request);
            
            PageResult<ShopSubscribeResponse> pageResult = new PageResult<>(
                    responses, total, request.getPage(), request.getPageSize());
            
            log.info("[EXTERNAL] 分页查询店铺订购记录成功: 数量={}, 总数={}", responses.size(), total);
            return Result.success("查询成功", pageResult);
        } catch (Exception e) {
            log.error("[EXTERNAL] 分页查询店铺订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新店铺订购记录
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新店铺订购记录", description = "根据ID更新店铺订购记录")
    public Result<String> updateById(
            @Parameter(description = "API密钥", required = true)
            @RequestHeader(value = "X-API-KEY", required = false) String apiKey,
            @Parameter(description = "订购记录ID", required = true, example = "1")
            @PathVariable Long id,
            @Valid @RequestBody ShopSubscribeRequest request) {
        
        log.info("[EXTERNAL] 收到更新店铺订购记录请求: id={}, shopId={}", id, request.getShopId());
        
        if (!validateApiKey(apiKey)) {
            return Result.error(401, "API密钥无效");
        }
        
        try {
            if (id == null) {
                return Result.error("ID不能为空");
            }
            
            boolean success = shopSubscribeService.updateById(id, request);
            
            if (success) {
                log.info("[EXTERNAL] 更新店铺订购记录成功: id={}", id);
                return Result.success("更新成功");
            } else {
                log.warn("[EXTERNAL] 更新店铺订购记录失败，记录可能不存在: id={}", id);
                return Result.error("更新失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("[EXTERNAL] 更新店铺订购记录失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 查询即将过期的订购记录
     */
    @GetMapping("/expiring")
    @Operation(summary = "查询即将过期的订购记录", description = "查询指定天数内即将过期的订购记录")
    public Result<List<ShopSubscribeResponse>> findExpiringSubscriptions(
            @Parameter(description = "API密钥", required = true)
            @RequestHeader(value = "X-API-KEY", required = false) String apiKey,
            @Parameter(description = "天数", required = true, example = "7")
            @RequestParam Integer days) {
        
        log.info("[EXTERNAL] 收到查询即将过期订购记录请求: days={}", days);
        
        if (!validateApiKey(apiKey)) {
            return Result.error(401, "API密钥无效");
        }
        
        try {
            if (days == null || days < 0) {
                return Result.error("天数参数无效");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findExpiringSubscriptions(days);
            
            log.info("[EXTERNAL] 查询即将过期订购记录成功: days={}, 数量={}", days, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[EXTERNAL] 查询即将过期订购记录失败: days={}", days, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 验证API密钥
     * 这里可以根据实际需求实现具体的验证逻辑
     */
    private boolean validateApiKey(String apiKey) {
        // 简单的API密钥验证示例
        // 实际项目中应该从数据库或配置文件中获取有效的API密钥
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }
        
        // 示例：检查API密钥格式和有效性
        return apiKey.startsWith("sk-") && apiKey.length() >= 20;
    }
}
