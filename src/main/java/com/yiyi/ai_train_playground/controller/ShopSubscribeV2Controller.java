package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ShopSubscribeQueryRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeResponse;
import com.yiyi.ai_train_playground.service.ShopSubscribeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 店铺订购控制器 V2版本 - 供外部程序调用
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/v2/shop-subscribe")
@RequiredArgsConstructor
@Tag(name = "店铺订购管理V2", description = "店铺订购相关接口V2版本，供外部程序调用")
public class ShopSubscribeV2Controller {

    private final ShopSubscribeService shopSubscribeService;

    /**
     * 创建店铺订购记录
     */
    @PostMapping
    @Operation(summary = "创建店铺订购记录", description = "创建新的店铺订购记录")
    public Result<String> create(@Valid @RequestBody ShopSubscribeRequest request) {
        log.info("[V2] 收到创建店铺订购记录请求: shopId={}, shopType={}", request.getShopId(), request.getShopType());
        
        try {
            boolean success = shopSubscribeService.create(request);
            
            if (success) {
                log.info("[V2] 创建店铺订购记录成功: shopId={}", request.getShopId());
                return Result.success("创建成功");
            } else {
                log.warn("[V2] 创建店铺订购记录失败: shopId={}", request.getShopId());
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("[V2] 创建店铺订购记录失败: shopId={}", request.getShopId(), e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询店铺订购记录
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询店铺订购记录", description = "根据ID查询店铺订购记录详情")
    public Result<ShopSubscribeResponse> findById(
            @Parameter(description = "订购记录ID", required = true, example = "1")
            @PathVariable Long id) {
        log.info("[V2] 收到查询店铺订购记录请求: id={}", id);
        
        try {
            if (id == null) {
                return Result.error("ID不能为空");
            }
            
            ShopSubscribeResponse response = shopSubscribeService.findById(id);
            
            if (response != null) {
                log.info("[V2] 查询店铺订购记录成功: id={}", id);
                return Result.success("查询成功", response);
            } else {
                log.info("[V2] 未找到店铺订购记录: id={}", id);
                return Result.error("未找到对应的订购记录");
            }
        } catch (Exception e) {
            log.error("[V2] 查询店铺订购记录失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据店铺ID查询订购记录
     */
    @GetMapping("/shop/{shopId}")
    @Operation(summary = "根据店铺ID查询订购记录", description = "根据店铺ID查询该店铺的所有订购记录")
    public Result<List<ShopSubscribeResponse>> findByShopId(
            @Parameter(description = "店铺ID", required = true, example = "12345")
            @PathVariable Long shopId) {
        log.info("[V2] 收到根据店铺ID查询订购记录请求: shopId={}", shopId);
        
        try {
            if (shopId == null) {
                return Result.error("店铺ID不能为空");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findByShopId(shopId);
            
            log.info("[V2] 根据店铺ID查询订购记录成功: shopId={}, 数量={}", shopId, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[V2] 根据店铺ID查询订购记录失败: shopId={}", shopId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询有效的订购记录（未过期）
     */
    @GetMapping("/valid")
    @Operation(summary = "查询有效的订购记录", description = "查询所有未过期的订购记录")
    public Result<List<ShopSubscribeResponse>> findValidSubscriptions() {
        log.info("[V2] 收到查询有效订购记录请求");
        
        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptions();
            
            log.info("[V2] 查询有效订购记录成功: 数量={}", responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[V2] 查询有效订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据店铺ID查询有效的订购记录
     */
    @GetMapping("/valid/shop/{shopId}")
    @Operation(summary = "根据店铺ID查询有效的订购记录", description = "根据店铺ID查询该店铺的有效订购记录")
    public Result<List<ShopSubscribeResponse>> findValidSubscriptionsByShopId(
            @Parameter(description = "店铺ID", required = true, example = "12345")
            @PathVariable Long shopId) {
        log.info("[V2] 收到根据店铺ID查询有效订购记录请求: shopId={}", shopId);
        
        try {
            if (shopId == null) {
                return Result.error("店铺ID不能为空");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptionsByShopId(shopId);
            
            log.info("[V2] 根据店铺ID查询有效订购记录成功: shopId={}, 数量={}", shopId, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[V2] 根据店铺ID查询有效订购记录失败: shopId={}", shopId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询店铺订购记录
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询店铺订购记录", description = "根据条件分页查询店铺订购记录")
    public Result<PageResult<ShopSubscribeResponse>> findWithPagination(@RequestBody ShopSubscribeQueryRequest request) {
        log.info("[V2] 收到分页查询店铺订购记录请求: page={}, pageSize={}", request.getPage(), request.getPageSize());
        
        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findWithPagination(request);
            Long total = shopSubscribeService.countByConditions(request);
            
            PageResult<ShopSubscribeResponse> pageResult = new PageResult<>(
                    responses, total, request.getPage(), request.getPageSize());
            
            log.info("[V2] 分页查询店铺订购记录成功: 数量={}, 总数={}", responses.size(), total);
            return Result.success("查询成功", pageResult);
        } catch (Exception e) {
            log.error("[V2] 分页查询店铺订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新店铺订购记录
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新店铺订购记录", description = "根据ID更新店铺订购记录")
    public Result<String> updateById(
            @Parameter(description = "订购记录ID", required = true, example = "1")
            @PathVariable Long id,
            @Valid @RequestBody ShopSubscribeRequest request) {
        log.info("[V2] 收到更新店铺订购记录请求: id={}, shopId={}", id, request.getShopId());
        
        try {
            if (id == null) {
                return Result.error("ID不能为空");
            }
            
            boolean success = shopSubscribeService.updateById(id, request);
            
            if (success) {
                log.info("[V2] 更新店铺订购记录成功: id={}", id);
                return Result.success("更新成功");
            } else {
                log.warn("[V2] 更新店铺订购记录失败，记录可能不存在: id={}", id);
                return Result.error("更新失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("[V2] 更新店铺订购记录失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 根据买家pin查询订购记录
     */
    @GetMapping("/buyer/{buyer}")
    @Operation(summary = "根据买家pin查询订购记录", description = "根据买家pin查询订购记录列表")
    public Result<List<ShopSubscribeResponse>> findByBuyer(
            @Parameter(description = "买家pin", required = true, example = "buyer123")
            @PathVariable String buyer) {
        log.info("[V2] 收到根据买家pin查询订购记录请求: buyer={}", buyer);
        
        try {
            if (buyer == null || buyer.trim().isEmpty()) {
                return Result.error("买家pin不能为空");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findByBuyer(buyer);
            
            log.info("[V2] 根据买家pin查询订购记录成功: buyer={}, 数量={}", buyer, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[V2] 根据买家pin查询订购记录失败: buyer={}", buyer, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询即将过期的订购记录
     */
    @GetMapping("/expiring")
    @Operation(summary = "查询即将过期的订购记录", description = "查询指定天数内即将过期的订购记录")
    public Result<List<ShopSubscribeResponse>> findExpiringSubscriptions(
            @Parameter(description = "天数", required = true, example = "7")
            @RequestParam Integer days) {
        log.info("[V2] 收到查询即将过期订购记录请求: days={}", days);
        
        try {
            if (days == null || days < 0) {
                return Result.error("天数参数无效");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findExpiringSubscriptions(days);
            
            log.info("[V2] 查询即将过期订购记录成功: days={}, 数量={}", days, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("[V2] 查询即将过期订购记录失败: days={}", days, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
