package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.ShopWithTokenResponse;
import com.yiyi.ai_train_playground.entity.TrainTeamShopWithToken;
import com.yiyi.ai_train_playground.service.TrainTeamShopsService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队店铺控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/team-shops")
@RequiredArgsConstructor
@Tag(name = "团队店铺管理", description = "团队店铺相关接口")
public class TrainTeamShopsController {

    private final TrainTeamShopsService trainTeamShopsService;

    /**
     * 根据店铺ID查询店铺信息及对应的京东token信息
     *
     * @param shopId 店铺ID
     * @return 包含店铺和token信息的实体对象
     */
    @GetMapping("/shop/{shopId}")
    @Operation(
        summary = "根据店铺ID查询店铺信息及token",
        description = "根据店铺ID查询店铺信息及对应的京东token信息，包含授权状态、同步状态等"
    )
    public Result<ShopWithTokenResponse> getShopAndTokenByShopId(
            @Parameter(description = "店铺ID", required = true, example = "12345")
            @PathVariable String shopId) {
        
        log.info("收到根据店铺ID查询店铺信息及token请求: shopId={}", shopId);
        
        try {
            // 参数校验
            if (shopId == null || shopId.trim().isEmpty()) {
                log.warn("店铺ID不能为空");
                return Result.error("店铺ID不能为空");
            }
            
            // 调用服务层查询
            TrainTeamShopWithToken result = trainTeamShopsService.selectShopAndTokenByShopId(shopId);
            
            if (result == null) {
                log.info("未找到店铺ID为{}的记录", shopId);
                return Result.error("未找到对应的店铺信息");
            }
            
            // 转换为响应DTO
            ShopWithTokenResponse response = ShopWithTokenResponse.fromEntity(result);

            log.info("根据店铺ID查询店铺信息及token成功: shopId={}, teamId={}",
                    shopId, result.getTeamId());
            return Result.success("查询成功", response);
            
        } catch (Exception e) {
            log.error("根据店铺ID查询店铺信息及token失败: shopId={}", shopId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据团队ID查询未过期的店铺列表信息及对应的京东token信息
     *
     * @param teamId 团队ID（可选，如果不传则使用当前登录用户的团队ID）
     * @return 包含未过期店铺列表和token信息的实体集合
     */
    @GetMapping("/valid-list")
    @Operation(
        summary = "根据团队ID查询未过期的店铺列表及token",
        description = "根据团队ID查询未过期的店铺列表信息及对应的京东token信息，如果不传teamId则使用当前登录用户的团队ID"
    )
    public Result<List<ShopWithTokenResponse>> getValidShopAndTokenListByTeamId(
            @Parameter(description = "团队ID（可选）", example = "1001")
            @RequestParam(required = false) String teamId) {

        log.info("收到根据团队ID查询未过期店铺列表及token请求: teamId={}", teamId);

        try {
            // 如果没有传入teamId，则从当前登录用户获取
            String finalTeamId = teamId;
            if (finalTeamId == null || finalTeamId.trim().isEmpty()) {
                Long currentTeamId = SecurityUtil.getCurrentTeamId();
                if (currentTeamId == null) {
                    log.warn("无法获取当前用户的团队ID，且未传入teamId参数");
                    return Result.error("无法获取团队ID，请传入teamId参数或确保已登录");
                }
                finalTeamId = currentTeamId.toString();
                log.debug("使用当前登录用户的团队ID: {}", finalTeamId);
            }

            // 调用服务层查询未过期的店铺
            List<TrainTeamShopWithToken> result = trainTeamShopsService.selectValidShopAndTokenByTeamId(finalTeamId);

            if (result == null || result.isEmpty()) {
                log.info("团队ID为{}的未过期店铺列表为空", finalTeamId);
                return Result.success("查询成功，但没有找到未过期的店铺数据", List.of());
            }

            // 转换为响应DTO列表
            List<ShopWithTokenResponse> responseList = result.stream()
                    .map(ShopWithTokenResponse::fromEntity)
                    .collect(Collectors.toList());

            log.info("根据团队ID查询未过期店铺列表及token成功: teamId={}, 数量={}",
                    finalTeamId, result.size());
            return Result.success("查询成功", responseList);

        } catch (Exception e) {
            log.error("根据团队ID查询未过期店铺列表及token失败: teamId={}", teamId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据当前登录用户的团队ID查询未过期的店铺列表信息及对应的京东token信息
     *
     * @return 包含未过期店铺列表和token信息的实体集合
     */
    @GetMapping("/my-valid-shops")
    @Operation(
        summary = "查询当前用户的未过期店铺列表及token",
        description = "根据当前登录用户的团队ID查询未过期的店铺列表信息及对应的京东token信息"
    )
    public Result<List<ShopWithTokenResponse>> getMyValidShopsAndToken() {

        log.info("收到查询当前用户未过期店铺列表及token请求");

        try {
            // 获取当前登录用户的团队ID
            Long currentTeamId = SecurityUtil.getCurrentTeamId();
            if (currentTeamId == null) {
                log.warn("无法获取当前用户的团队ID");
                return Result.error("用户未登录或无法获取团队信息");
            }

            String teamId = currentTeamId.toString();
            log.debug("当前用户团队ID: {}", teamId);

            // 调用服务层查询未过期的店铺
            List<TrainTeamShopWithToken> result = trainTeamShopsService.selectValidShopAndTokenByTeamId(teamId);

            if (result == null || result.isEmpty()) {
                log.info("当前用户团队ID为{}的未过期店铺列表为空", teamId);
                return Result.success("查询成功，但没有找到未过期的店铺数据", List.of());
            }

            // 转换为响应DTO列表
            List<ShopWithTokenResponse> responseList = result.stream()
                    .map(ShopWithTokenResponse::fromEntity)
                    .collect(Collectors.toList());

            log.info("查询当前用户未过期店铺列表及token成功: teamId={}, 数量={}",
                    teamId, result.size());
            return Result.success("查询成功", responseList);

        } catch (Exception e) {
            log.error("查询当前用户未过期店铺列表及token失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
