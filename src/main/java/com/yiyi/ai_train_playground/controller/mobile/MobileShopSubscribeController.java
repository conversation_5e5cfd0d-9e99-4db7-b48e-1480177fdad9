package com.yiyi.ai_train_playground.controller.mobile;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ShopSubscribeQueryRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeResponse;
import com.yiyi.ai_train_playground.service.ShopSubscribeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 移动端店铺订购控制器
 * 专为移动端应用优化的API接口
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/mobile/api/shop-subscribe")
@RequiredArgsConstructor
@Tag(name = "移动端店铺订购管理", description = "专为移动端优化的店铺订购相关接口")
public class MobileShopSubscribeController {

    private final ShopSubscribeService shopSubscribeService;

    /**
     * 根据店铺ID查询有效的订购记录（移动端简化版）
     */
    @GetMapping("/valid/shop/{shopId}")
    @Operation(summary = "查询店铺有效订购记录", description = "移动端专用：根据店铺ID查询有效订购记录")
    public Result<Map<String, Object>> findValidSubscriptionsByShopIdMobile(
            @Parameter(description = "店铺ID", required = true, example = "12345")
            @PathVariable Long shopId) {
        
        log.info("[MOBILE] 收到查询店铺有效订购记录请求: shopId={}", shopId);
        
        try {
            if (shopId == null) {
                return Result.error("店铺ID不能为空");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptionsByShopId(shopId);
            
            // 移动端优化：返回简化的数据结构
            Map<String, Object> mobileResponse = new HashMap<>();
            mobileResponse.put("shopId", shopId);
            mobileResponse.put("hasValidSubscription", !responses.isEmpty());
            mobileResponse.put("subscriptionCount", responses.size());
            
            if (!responses.isEmpty()) {
                // 只返回最新的订购记录
                ShopSubscribeResponse latest = responses.get(0);
                Map<String, Object> latestSubscription = new HashMap<>();
                latestSubscription.put("id", latest.getId());
                latestSubscription.put("shopTypeName", latest.getShopTypeName());
                latestSubscription.put("statusName", latest.getStatusName());
                latestSubscription.put("version", latest.getVersion());
                latestSubscription.put("endTime", latest.getEndTime());
                latestSubscription.put("isValid", latest.getIsValid());
                
                mobileResponse.put("latestSubscription", latestSubscription);
            }
            
            log.info("[MOBILE] 查询店铺有效订购记录成功: shopId={}, 数量={}", shopId, responses.size());
            return Result.success("查询成功", mobileResponse);
        } catch (Exception e) {
            log.error("[MOBILE] 查询店铺有效订购记录失败: shopId={}", shopId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户的所有有效订购记录（移动端简化版）
     */
    @GetMapping("/my-valid")
    @Operation(summary = "查询我的有效订购记录", description = "移动端专用：查询当前用户的所有有效订购记录")
    public Result<Map<String, Object>> findMyValidSubscriptions(
            @Parameter(description = "用户ID", required = false)
            @RequestParam(required = false) Long userId) {
        
        log.info("[MOBILE] 收到查询我的有效订购记录请求: userId={}", userId);
        
        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptions();
            
            // 移动端优化：按店铺类型分组
            Map<String, Object> mobileResponse = new HashMap<>();
            Map<String, Integer> typeCount = new HashMap<>();
            Map<String, List<Map<String, Object>>> typeSubscriptions = new HashMap<>();
            
            for (ShopSubscribeResponse response : responses) {
                String typeName = response.getShopTypeName();
                typeCount.put(typeName, typeCount.getOrDefault(typeName, 0) + 1);
                
                // 简化的订购信息
                Map<String, Object> simpleSubscription = new HashMap<>();
                simpleSubscription.put("id", response.getId());
                simpleSubscription.put("shopId", response.getShopId());
                simpleSubscription.put("version", response.getVersion());
                simpleSubscription.put("endTime", response.getEndTime());
                
                typeSubscriptions.computeIfAbsent(typeName, k -> new java.util.ArrayList<>())
                        .add(simpleSubscription);
            }
            
            mobileResponse.put("totalCount", responses.size());
            mobileResponse.put("typeCount", typeCount);
            mobileResponse.put("subscriptionsByType", typeSubscriptions);
            
            log.info("[MOBILE] 查询我的有效订购记录成功: 总数={}", responses.size());
            return Result.success("查询成功", mobileResponse);
        } catch (Exception e) {
            log.error("[MOBILE] 查询我的有效订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询即将过期的订购记录（移动端简化版）
     */
    @GetMapping("/expiring")
    @Operation(summary = "查询即将过期的订购记录", description = "移动端专用：查询即将过期的订购记录")
    public Result<Map<String, Object>> findExpiringSubscriptionsMobile(
            @Parameter(description = "天数", example = "7")
            @RequestParam(defaultValue = "7") Integer days) {
        
        log.info("[MOBILE] 收到查询即将过期订购记录请求: days={}", days);
        
        try {
            if (days == null || days < 0) {
                days = 7; // 默认7天
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findExpiringSubscriptions(days);
            
            // 移动端优化：返回告警信息
            Map<String, Object> mobileResponse = new HashMap<>();
            mobileResponse.put("alertCount", responses.size());
            mobileResponse.put("checkDays", days);
            
            if (!responses.isEmpty()) {
                List<Map<String, Object>> alerts = new java.util.ArrayList<>();
                for (ShopSubscribeResponse response : responses) {
                    Map<String, Object> alert = new HashMap<>();
                    alert.put("shopId", response.getShopId());
                    alert.put("shopTypeName", response.getShopTypeName());
                    alert.put("version", response.getVersion());
                    alert.put("endTime", response.getEndTime());
                    alert.put("urgency", calculateUrgency(response.getEndTime()));
                    alerts.add(alert);
                }
                mobileResponse.put("alerts", alerts);
            }
            
            log.info("[MOBILE] 查询即将过期订购记录成功: days={}, 数量={}", days, responses.size());
            return Result.success("查询成功", mobileResponse);
        } catch (Exception e) {
            log.error("[MOBILE] 查询即将过期订购记录失败: days={}", days, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 移动端分页查询（简化版）
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询订购记录", description = "移动端专用：分页查询订购记录")
    public Result<Map<String, Object>> findWithPaginationMobile(@RequestBody ShopSubscribeQueryRequest request) {
        log.info("[MOBILE] 收到分页查询请求: page={}, pageSize={}", request.getPage(), request.getPageSize());
        
        try {
            // 移动端默认较小的页面大小
            if (request.getPageSize() == null || request.getPageSize() > 20) {
                request.setPageSize(10);
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findWithPagination(request);
            Long total = shopSubscribeService.countByConditions(request);
            
            // 移动端优化的分页响应
            Map<String, Object> mobileResponse = new HashMap<>();
            mobileResponse.put("items", responses);
            mobileResponse.put("pagination", Map.of(
                "current", request.getPage(),
                "pageSize", request.getPageSize(),
                "total", total,
                "hasMore", (long) request.getPage() * request.getPageSize() < total
            ));
            
            log.info("[MOBILE] 分页查询成功: 数量={}, 总数={}", responses.size(), total);
            return Result.success("查询成功", mobileResponse);
        } catch (Exception e) {
            log.error("[MOBILE] 分页查询失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 计算紧急程度
     */
    private String calculateUrgency(java.time.LocalDateTime endTime) {
        if (endTime == null) {
            return "unknown";
        }
        
        long daysUntilExpiry = java.time.temporal.ChronoUnit.DAYS.between(
                java.time.LocalDateTime.now(), endTime);
        
        if (daysUntilExpiry <= 1) {
            return "critical";
        } else if (daysUntilExpiry <= 3) {
            return "high";
        } else if (daysUntilExpiry <= 7) {
            return "medium";
        } else {
            return "low";
        }
    }
}
