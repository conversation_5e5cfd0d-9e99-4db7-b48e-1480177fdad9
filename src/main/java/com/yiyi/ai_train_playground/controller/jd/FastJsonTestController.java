package com.yiyi.ai_train_playground.controller.jd;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yiyi.ai_train_playground.entity.AuthorizationUserInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * FastJSON测试控制器
 * 用于验证FastJSON依赖和state解析功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
@RestController
@RequestMapping("/api/test/fastjson")
@Tag(name = "FastJSON测试", description = "测试FastJSON依赖和state解析功能")
public class FastJsonTestController {

    @GetMapping("/test-dependency")
    @Operation(summary = "测试FastJSON依赖", description = "验证FastJSON依赖是否正常工作")
    public Object testFastJsonDependency() {
        try {
            // 创建一个简单的JSON对象测试
            JSONObject json = new JSONObject();
            json.put("test", "FastJSON依赖正常工作");
            json.put("version", "1.2.83");
            json.put("timestamp", System.currentTimeMillis());
            
            return json;
        } catch (Exception e) {
            log.error("FastJSON依赖测试失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @GetMapping("/test-type-reference")
    @Operation(summary = "测试TypeReference", description = "测试TypeReference是否能正常使用")
    public Object testTypeReference() {
        try {
            String jsonStr = "{\"jos_parameters\":{\"user_name\":\"testuser\",\"end_date\":1640995200000,\"item_code\":\"TEST-001\"}}";
            
            Map<String, AuthorizationUserInfo> userMap = JSONObject.parseObject(jsonStr, new TypeReference<Map<String, AuthorizationUserInfo>>() {});
            AuthorizationUserInfo user = userMap.get("jos_parameters");
            
            return Map.of(
                "success", true,
                "user", user,
                "userName", user != null ? user.getUser_name() : null,
                "endDate", user != null ? user.getEnd_date() : null,
                "itemCode", user != null ? user.getItem_code() : null
            );
        } catch (Exception e) {
            log.error("TypeReference测试失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @PostMapping("/test-state-parse")
    @Operation(summary = "测试state解析", description = "测试完整的state参数解析流程")
    public Object testStateParse(@RequestBody Map<String, String> request) {
        try {
            String state = request.get("state");
            if (state == null || state.isEmpty()) {
                return "Error: state参数不能为空";
            }
            
            // 模拟完整的state解析流程
            String states = "";
            Pattern p = Pattern.compile("\\s");
            Matcher m = p.matcher(state);
            if (m.find()) {
                states = m.replaceAll("+");
            } else {
                states = state;
            }
            
            log.info("转换后states: {}", states);
            
            // Base64解码
            states = new String(Base64.getDecoder().decode(states), "UTF-8");
            log.info("Base64解码后states: {}", states);
            
            // JSON解析
            Map<String, AuthorizationUserInfo> userMap = JSONObject.parseObject(states, new TypeReference<Map<String, AuthorizationUserInfo>>() {});
            AuthorizationUserInfo user = userMap.get("jos_parameters");
            
            return Map.of(
                "success", true,
                "originalState", state,
                "processedState", states,
                "parsedUser", user,
                "pin", user != null ? user.getUser_name() : null
            );
        } catch (Exception e) {
            log.error("state解析测试失败", e);
            return Map.of(
                "success", false,
                "error", e.getMessage(),
                "stackTrace", e.getStackTrace()
            );
        }
    }
}