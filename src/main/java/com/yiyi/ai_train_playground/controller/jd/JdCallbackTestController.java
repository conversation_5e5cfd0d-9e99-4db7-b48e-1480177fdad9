package com.yiyi.ai_train_playground.controller.jd;

import com.yiyi.ai_train_playground.entity.JDSessionKey;
import com.yiyi.ai_train_playground.entity.LoginUserParam;
import com.yiyi.ai_train_playground.entity.ShopInfoDO;
import com.yiyi.ai_train_playground.service.jd.JdCallbackProcessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 京东回调测试控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
@RestController
@RequestMapping("/api/test/jd")
@RequiredArgsConstructor
@Tag(name = "京东回调测试", description = "用于测试京东回调功能的接口")
public class JdCallbackTestController {

    private final JdCallbackProcessService jdCallbackProcessService;

    @GetMapping("/test-session-key")
    @Operation(summary = "测试获取SessionKey", description = "通过code测试获取京东SessionKey")
    public Object testGetSessionKey(@RequestParam String code) {
        try {
            JDSessionKey sessionKey = jdCallbackProcessService.getSessionKeyByCode(code);
            return sessionKey;
        } catch (Exception e) {
            log.error("测试获取SessionKey失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @GetMapping("/test-sub-account")
    @Operation(summary = "测试子账号判断", description = "测试判断是否为子账号")
    public Object testIsSubAccount(@RequestParam String sessionKey, @RequestParam String pin) {
        try {
            boolean isSubAccount = jdCallbackProcessService.isSubAccount(sessionKey, pin);
            return "isSubAccount: " + isSubAccount;
        } catch (Exception e) {
            log.error("测试子账号判断失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @GetMapping("/test-login-param")
    @Operation(summary = "测试构建LoginUserParam", description = "测试构建登录用户参数")
    public Object testBuildLoginUserParam(@RequestParam String code, @RequestParam String state) {
        try {
            JDSessionKey jdSessionKey = jdCallbackProcessService.getSessionKeyByCode(code);
            String pin = jdCallbackProcessService.getPinFromState(state);
            String openId = jdSessionKey.getOpen_id();
            LoginUserParam loginUserParam = jdCallbackProcessService.buildLoginUserParam(jdSessionKey, pin, openId, state);
            return loginUserParam;
        } catch (Exception e) {
            log.error("测试构建LoginUserParam失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @GetMapping("/test-shop-info")
    @Operation(summary = "测试构建ShopInfo", description = "测试构建店铺信息")
    public Object testBuildShopInfo(@RequestParam String pin, @RequestParam String sessionKey) {
        try {
            ShopInfoDO shopInfo = jdCallbackProcessService.buildShopInfo(pin, sessionKey);
            return shopInfo;
        } catch (Exception e) {
            log.error("测试构建ShopInfo失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @GetMapping("/test-pin-from-state")
    @Operation(summary = "测试从state解析PIN", description = "测试从state参数解析获取用户PIN")
    public Object testGetPinFromState(@RequestParam String state) {
        try {
            String pin = jdCallbackProcessService.getPinFromState(state);
            return "PIN: " + pin;
        } catch (Exception e) {
            log.error("测试从state解析PIN失败", e);
            return "Error: " + e.getMessage();
        }
    }

    @GetMapping("/test-full-flow")
    @Operation(summary = "测试完整流程", description = "测试完整的回调处理流程")
    public Object testFullFlow(@RequestParam String code, @RequestParam String state) {
        try {
            // 1. 获取SessionKey
            JDSessionKey jdSessionKey = jdCallbackProcessService.getSessionKeyByCode(code);
            
            // 2. 从state解析PIN
            String pin = jdCallbackProcessService.getPinFromState(state);
            
            // 3. 检查是否为子账号
            boolean isSubAccount = jdCallbackProcessService.isSubAccount(jdSessionKey.getAccess_token(), pin);
            
            // 4. 构建LoginUserParam
            LoginUserParam loginUserParam = jdCallbackProcessService.buildLoginUserParam(jdSessionKey, pin, jdSessionKey.getOpen_id(), state);
            
            // 5. 构建ShopInfo
            ShopInfoDO shopInfo = jdCallbackProcessService.buildShopInfo(pin, jdSessionKey.getAccess_token());
            
            // 返回结果
            return Map.of(
                "sessionKey", jdSessionKey,
                "pin", pin,
                "isSubAccount", isSubAccount,
                "loginUserParam", loginUserParam,
                "shopInfo", shopInfo
            );
        } catch (Exception e) {
            log.error("测试完整流程失败", e);
            return "Error: " + e.getMessage();
        }
    }
}