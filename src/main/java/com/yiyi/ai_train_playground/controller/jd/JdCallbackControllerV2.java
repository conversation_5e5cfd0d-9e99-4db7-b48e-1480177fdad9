package com.yiyi.ai_train_playground.controller.jd;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.dto.JdCallbackRequest;
import com.yiyi.ai_train_playground.dto.JdCallbackResult;
import com.yiyi.ai_train_playground.entity.JDSessionKey;
import com.yiyi.ai_train_playground.entity.LoginUserParam;
import com.yiyi.ai_train_playground.entity.ShopInfoDO;
import com.yiyi.ai_train_playground.service.jd.JdCallbackServiceV2;
import com.yiyi.ai_train_playground.service.jd.JdCallbackProcessService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

/**
 * 京东回调接口V2
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@Tag(name = "京东回调接口V2", description = "处理京东OAuth2授权回调V2")
public class JdCallbackControllerV2 {

    private final JdCallbackServiceV2 jdCallbackServiceV2;
    private final JdCallbackProcessService jdCallbackProcessService;
    private final JdConfig jdConfig;
    private final JwtUtil jwtUtil;

    @Value("${pg-guide.auth-url}")
    private String pgGuideAuthUrl;

    @Value("${pg-guide.bind-xid-url}")
    private String pgGuideBindXidUrl;
    
    /**
     * 京东回调接口V2
     * 
     * @param state 随机数状态码
     * @param code 京东返回的授权码
     * @param request HTTP请求对象
     * @return 重定向到前端页面
     */
    @GetMapping("/api/v2/yiyicallback")
    @Operation(
        summary = "京东OAuth2回调接口V2",
        description = "接收京东OAuth2授权回调，验证state参数，调用京东API获取access_token并保存到数据库，设置授权和同步状态",
        parameters = {
            @Parameter(name = "state", description = "随机数状态码，用于防止CSRF攻击", required = true, example = "YyJdPlayground2025"),
            @Parameter(name = "code", description = "京东返回的授权码", required = true, example = "test-code-123")
        }
    )
    public String handleJdCallback(HttpServletRequest request,String state, String code, String error, String error_description) {
        log.info("收到京东回调请求，state: {}, code: {}, error: {}, error_description: {}", state, code, error, error_description);
        
        // 处理授权错误
        if (error != null) {
            log.info("网页登录失败：[code:{}, error:{}]", code, error_description);
            if ("access_denied".equals(error)) {
                return "redirect:" + jdConfig.getRedirectSubAccountAccess() + "?error=permission_denied";
            }
            return "redirect:" + jdConfig.getRedirectSubAccountAccess() + "?error=" + error;
        }

        // 验证必要参数
        if (!org.springframework.util.StringUtils.hasText(code)) {
            log.error("授权码code为空");
            return "redirect:" + jdConfig.getRedirectSubAccountAccess() + "?error=invalid_code";
        }

        try {
            // 1. 通过code获取京东SessionKey
            JDSessionKey jdSessionKey = jdCallbackProcessService.getSessionKeyByCode(code);
            String openId = jdSessionKey.getOpen_id();
            String sessionKey = jdSessionKey.getAccess_token();
            String uid = jdSessionKey.getUid();
            
            // 2. 从state参数解析获取用户PIN（使用老项目的方法）
            String pin = jdCallbackProcessService.getPinFromState(state);
            
            log.info("成功获取京东SessionKey, uid: {}, openId: {}, pin: {}", uid, openId, pin);
            
            // 3. 判断是否为子账号，如果是则拦截
            if (jdCallbackProcessService.isSubAccount(sessionKey, pin)) {
                log.warn("检测到子账号登录，拒绝访问, pin: {}", pin);
                return "redirect:" + jdConfig.getRedirectSubAccountAccess() + "?error=sub_account_denied";
            }
            
            // 4. 构建LoginUserParam对象
            LoginUserParam loginUserParam = jdCallbackProcessService.buildLoginUserParam(jdSessionKey, pin, openId, state);
            log.info("构建LoginUserParam成功: {}", loginUserParam);
            
            // 5. 构建ShopInfoDO对象  
            ShopInfoDO shopInfo = jdCallbackProcessService.buildShopInfo(pin, sessionKey);
            log.info("构建ShopInfo成功: {}", shopInfo);
            
            // 6. 更新LoginUserParam中的店铺信息
            loginUserParam.setShopId(shopInfo.getShopId());
            loginUserParam.setVenderType(shopInfo.getVenderType());

            // 7. 构建请求DTO
            JdCallbackRequest callbackRequest = new JdCallbackRequest();
            callbackRequest.setState(state);
            callbackRequest.setCode(code);
            // 8. 调用服务层处理业务逻辑
            JdCallbackResult result = jdCallbackServiceV2.handleCallback(callbackRequest);
            
            // 4. 根据处理结果重定向
            if (result.isSuccess()) {
                if (result.isJdUserExist()) {
                    // 用户已存在，跳转到PG Guide认证页面
                    String redirectUrl = pgGuideAuthUrl + "/" + result.getUserId();
                    log.info("用户已存在，重定向到PG Guide认证页面: {}", redirectUrl);
                    return "redirect:" + redirectUrl;
                } else {
                    // 用户不存在，跳转到绑定XID页面,重定向用户注册页面,用户输入手机号进行绑定
                    String xid = result.getXid();
                    String redirectUrl = pgGuideBindXidUrl + "?xid=" + xid;
                    log.info("用户不存在，重定向到绑定XID页面: {}", redirectUrl);
                    return "redirect:" + redirectUrl;
                }
            } else {
                log.error("京东回调处理失败: {}", result.getErrorMessage());
                return "redirect:" + jdConfig.getRedirectBaseUrl() + "?isAuthorize=0&is_sync_complete=0";
            }
            
        } catch (Exception e) {
            log.error("处理京东回调时发生异常", e);
            return "redirect:" + jdConfig.getRedirectBaseUrl() + "?isAuthorize=0&is_sync_complete=0";
        }
    }
    

    
    /**
     * 从HTTP请求中提取JWT token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        
        // 也可以从参数中获取token
        String tokenParam = request.getParameter("token");
        if (tokenParam != null && !tokenParam.isEmpty()) {
            return tokenParam;
        }
        
        return null;
    }
}
