package com.yiyi.ai_train_playground.controller.sms;

import com.pig4cloud.captcha.base.Captcha;
import com.pig4cloud.captcha.SpecCaptcha;
import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.CaptchaResponse;
import com.yiyi.ai_train_playground.dto.SmsCodeRequest;
import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;
import com.yiyi.ai_train_playground.dto.SmsVerifyRequest;
import com.yiyi.ai_train_playground.dto.SmsVerifyResponse;
import com.yiyi.ai_train_playground.service.sms.SmsServiceV2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 短信验证码控制器V2
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/api/v2/sms")
@RequiredArgsConstructor
@Validated
@Tag(name = "短信验证码V2", description = "短信验证码相关接口V2")
public class SmsControllerV2 {

    private final SmsServiceV2 smsServiceV2;
    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${pg-guide.auth-url}")
    private String pgGuideAuthUrl;
    
    /**
     * 生成图形验证码
     */
    @GetMapping("/captcha")
    @Operation(summary = "生成图形验证码", description = "生成图形验证码，返回验证码key和base64图片")
    public Result<CaptchaResponse> captcha() {
        try {
            log.info("生成图形验证码请求");
            
            // 生成验证码
            Captcha captcha = new SpecCaptcha(130, 48, 5);
            String captchaCode = captcha.text();
            String captchaImage = captcha.toBase64();
            
            // 生成唯一key
            String captchaKey = "captcha:" + UUID.randomUUID().toString().replace("-", "");
            
            // 存储到Redis，5分钟过期
            redisTemplate.opsForValue().set(captchaKey, captchaCode.toLowerCase(), 5, TimeUnit.MINUTES);
            
            CaptchaResponse response = new CaptchaResponse(captchaKey, captchaImage);
            
            log.info("图形验证码生成成功，key: {}", captchaKey);
            return Result.success("生成成功", response);
        } catch (Exception e) {
            log.error("生成图形验证码失败", e);
            return Result.error("生成验证码失败：" + e.getMessage());
        }
    }
    
    /**
     * 发送短信验证码
     */
    @PostMapping("/code")
    @Operation(summary = "发送短信验证码", description = "向指定手机号发送验证码")
    public Result<SmsCodeResponse> sendCode(@Valid @RequestBody SmsCodeRequest request) {
        try {
            // 额外的手动验证，确保phone参数存在
            if (request == null) {
                log.warn("请求体为空");
                return Result.error("请求体不能为空");
            }
            
            if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
                log.warn("手机号参数为空");
                return Result.error("手机号不能为空");
            }
            
            if (!StringUtils.hasText(request.getCaptchaKey())) {
                log.warn("验证码key参数为空");
                return Result.error("验证码key不能为空");
            }
            
            if (!StringUtils.hasText(request.getCaptchaCode())) {
                log.warn("图形验证码参数为空");
                return Result.error("图形验证码不能为空");
            }
            
            // 从Redis验证图形验证码
            String storedCaptchaCode = (String) redisTemplate.opsForValue().get(request.getCaptchaKey());
            if (storedCaptchaCode == null) {
                log.warn("验证码已过期或不存在，key: {}", request.getCaptchaKey());
                return Result.error("验证码已过期，请重新获取");
            }
            
            if (!storedCaptchaCode.equals(request.getCaptchaCode().toLowerCase())) {
                // 验证失败，删除Redis中的验证码
                redisTemplate.delete(request.getCaptchaKey());
                log.warn("图形验证码验证失败，key: {}, 输入: {}, 实际: {}", 
                    request.getCaptchaKey(), request.getCaptchaCode(), storedCaptchaCode);
                return Result.error("图形验证码错误");
            }
            
            // 验证成功后删除Redis中的验证码（一次性使用）
            redisTemplate.delete(request.getCaptchaKey());
            
            String phone = request.getPhone().trim();
            
            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                log.warn("手机号格式不正确: {}", phone);
                return Result.error("手机号格式不正确，请输入正确的中国大陆手机号");
            }
            
            log.info("收到发送短信验证码请求，手机号: {}，图形验证码验证通过", phone);
            SmsCodeResponse response = smsServiceV2.sendVerificationCode(phone);
            log.info("短信验证码发送成功，verificationKey: {}", response.getVerificationKey());
            return Result.success("发送成功", response);
        } catch (Exception e) {
            log.error("发送短信验证码失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 验证短信验证码并登录（返回重定向URL）
     */
    @PostMapping("/verify")
    @Operation(summary = "验证短信验证码并登录", description = "验证短信验证码，验证成功后自动创建团队和用户并返回重定向URL")
    public Result<SmsVerifyResponse> verifyAndLogin(@Valid @RequestBody SmsVerifyRequest request) {
        try {
            // 额外的手动验证
            if (request == null) {
                log.warn("请求体为空");
                return Result.error("请求体不能为空");
            }

            if (request.getVerificationKey() == null || request.getVerificationKey().trim().isEmpty()) {
                log.warn("验证密钥为空");
                return Result.error("验证密钥不能为空");
            }

            if (request.getVerificationCode() == null || request.getVerificationCode().trim().isEmpty()) {
                log.warn("验证码为空");
                return Result.error("验证码不能为空");
            }

            if (request.getXid() == null || request.getXid().trim().isEmpty()) {
                log.warn("xid为空");
                return Result.error("xid不能为空");
            }

            String verificationKey = request.getVerificationKey().trim();
            String verificationCode = request.getVerificationCode().trim();
            String xid = request.getXid().trim();

            // 验证验证码格式
            if (!verificationCode.matches("^\\d{4}$")) {
                log.warn("验证码格式不正确: {}", verificationCode);
                return Result.error("验证码必须是4位数字");
            }

            log.info("收到短信验证码验证请求，verificationKey: {}, xid: {}", verificationKey, xid);

            // 调用服务进行验证和登录
            SmsLoginResponse smsLoginResponse = smsServiceV2.smsLogin(verificationKey, verificationCode, null, xid);

            log.info("短信验证码验证成功，userId: {}, username: {}", smsLoginResponse.getUserId(), smsLoginResponse.getUsername());

            // 构建跳转URL
            String redirectUrl = pgGuideAuthUrl + "/" + smsLoginResponse.getUserId();
            log.info("生成重定向URL: {}", redirectUrl);

            // 创建响应对象
            SmsVerifyResponse response = SmsVerifyResponse.fromSmsLoginResponse(smsLoginResponse, redirectUrl);

            return Result.success("验证成功", response);

        } catch (Exception e) {
            log.error("短信验证码验证失败", e);
            return Result.error("验证失败：" + e.getMessage());
        }
    }
}
