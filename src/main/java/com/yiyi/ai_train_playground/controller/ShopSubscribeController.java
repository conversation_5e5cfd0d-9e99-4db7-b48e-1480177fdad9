package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ShopSubscribeQueryRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeResponse;
import com.yiyi.ai_train_playground.service.ShopSubscribeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 店铺订购控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/shop-subscribe")
@RequiredArgsConstructor
@Tag(name = "店铺订购管理", description = "店铺订购相关接口")
public class ShopSubscribeController {

    private final ShopSubscribeService shopSubscribeService;

    /**
     * 创建店铺订购记录
     */
    @PostMapping
    @Operation(summary = "创建店铺订购记录", description = "创建新的店铺订购记录")
    public Result<String> create(@Valid @RequestBody ShopSubscribeRequest request) {
        log.info("收到创建店铺订购记录请求: shopId={}, shopType={}", request.getShopId(), request.getShopType());
        
        try {
            boolean success = shopSubscribeService.create(request);
            
            if (success) {
                log.info("创建店铺订购记录成功: shopId={}", request.getShopId());
                return Result.success("创建成功");
            } else {
                log.warn("创建店铺订购记录失败: shopId={}", request.getShopId());
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建店铺订购记录失败: shopId={}", request.getShopId(), e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID删除店铺订购记录
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除店铺订购记录", description = "根据ID删除店铺订购记录")
    public Result<String> deleteById(
            @Parameter(description = "订购记录ID", required = true, example = "1")
            @PathVariable Long id) {
        log.info("收到删除店铺订购记录请求: id={}", id);
        
        try {
            // 参数校验
            if (id == null) {
                return Result.error("ID不能为空");
            }
            
            boolean success = shopSubscribeService.deleteById(id);
            
            if (success) {
                log.info("删除店铺订购记录成功: id={}", id);
                return Result.success("删除成功");
            } else {
                log.warn("删除店铺订购记录失败，记录可能不存在: id={}", id);
                return Result.error("删除失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("删除店铺订购记录失败: id={}", id, e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除店铺订购记录
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除店铺订购记录", description = "根据ID列表批量删除店铺订购记录")
    public Result<String> deleteByIds(@RequestBody List<Long> ids) {
        log.info("收到批量删除店铺订购记录请求: ids={}", ids);
        
        try {
            // 参数校验
            if (ids == null || ids.isEmpty()) {
                return Result.error("ID列表不能为空");
            }
            
            boolean success = shopSubscribeService.deleteByIds(ids);
            
            if (success) {
                log.info("批量删除店铺订购记录成功: ids={}", ids);
                return Result.success("批量删除成功");
            } else {
                log.warn("批量删除店铺订购记录失败: ids={}", ids);
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除店铺订购记录失败: ids={}", ids, e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新店铺订购记录
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新店铺订购记录", description = "根据ID更新店铺订购记录")
    public Result<String> updateById(
            @Parameter(description = "订购记录ID", required = true, example = "1")
            @PathVariable Long id,
            @Valid @RequestBody ShopSubscribeRequest request) {
        log.info("收到更新店铺订购记录请求: id={}, shopId={}", id, request.getShopId());
        
        try {
            // 参数校验
            if (id == null) {
                return Result.error("ID不能为空");
            }
            
            boolean success = shopSubscribeService.updateById(id, request);
            
            if (success) {
                log.info("更新店铺订购记录成功: id={}", id);
                return Result.success("更新成功");
            } else {
                log.warn("更新店铺订购记录失败，记录可能不存在: id={}", id);
                return Result.error("更新失败，记录可能不存在");
            }
        } catch (Exception e) {
            log.error("更新店铺订购记录失败: id={}", id, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询店铺订购记录
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询店铺订购记录", description = "根据ID查询店铺订购记录详情")
    public Result<ShopSubscribeResponse> findById(
            @Parameter(description = "订购记录ID", required = true, example = "1")
            @PathVariable Long id) {
        log.info("收到查询店铺订购记录请求: id={}", id);
        
        try {
            // 参数校验
            if (id == null) {
                return Result.error("ID不能为空");
            }
            
            ShopSubscribeResponse response = shopSubscribeService.findById(id);
            
            if (response != null) {
                log.info("查询店铺订购记录成功: id={}", id);
                return Result.success("查询成功", response);
            } else {
                log.info("未找到店铺订购记录: id={}", id);
                return Result.error("未找到对应的订购记录");
            }
        } catch (Exception e) {
            log.error("查询店铺订购记录失败: id={}", id, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有店铺订购记录
     */
    @GetMapping("/all")
    @Operation(summary = "查询所有店铺订购记录", description = "查询所有店铺订购记录列表")
    public Result<List<ShopSubscribeResponse>> findAll() {
        log.info("收到查询所有店铺订购记录请求");
        
        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findAll();
            
            log.info("查询所有店铺订购记录成功: 数量={}", responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("查询所有店铺订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据店铺ID查询订购记录
     */
    @GetMapping("/shop/{shopId}")
    @Operation(summary = "根据店铺ID查询订购记录", description = "根据店铺ID查询该店铺的所有订购记录")
    public Result<List<ShopSubscribeResponse>> findByShopId(
            @Parameter(description = "店铺ID", required = true, example = "12345")
            @PathVariable Long shopId) {
        log.info("收到根据店铺ID查询订购记录请求: shopId={}", shopId);
        
        try {
            // 参数校验
            if (shopId == null) {
                return Result.error("店铺ID不能为空");
            }
            
            List<ShopSubscribeResponse> responses = shopSubscribeService.findByShopId(shopId);
            
            log.info("根据店铺ID查询订购记录成功: shopId={}, 数量={}", shopId, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("根据店铺ID查询订购记录失败: shopId={}", shopId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据店铺类型查询订购记录
     */
    @GetMapping("/shop-type/{shopType}")
    @Operation(summary = "根据店铺类型查询订购记录", description = "根据店铺类型查询订购记录列表")
    public Result<List<ShopSubscribeResponse>> findByShopType(
            @Parameter(description = "店铺类型,0:京东，1：淘宝，2：抖店，3: 网盟入口", required = true, example = "0")
            @PathVariable Integer shopType) {
        log.info("收到根据店铺类型查询订购记录请求: shopType={}", shopType);

        try {
            // 参数校验
            if (shopType == null) {
                return Result.error("店铺类型不能为空");
            }

            List<ShopSubscribeResponse> responses = shopSubscribeService.findByShopType(shopType);

            log.info("根据店铺类型查询订购记录成功: shopType={}, 数量={}", shopType, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("根据店铺类型查询订购记录失败: shopType={}", shopType, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询有效的订购记录（未过期）
     */
    @GetMapping("/valid")
    @Operation(summary = "查询有效的订购记录", description = "查询所有未过期的订购记录")
    public Result<List<ShopSubscribeResponse>> findValidSubscriptions() {
        log.info("收到查询有效订购记录请求");

        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptions();

            log.info("查询有效订购记录成功: 数量={}", responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("查询有效订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据店铺ID查询有效的订购记录
     */
    @GetMapping("/valid/shop/{shopId}")
    @Operation(summary = "根据店铺ID查询有效的订购记录", description = "根据店铺ID查询该店铺的有效订购记录")
    public Result<List<ShopSubscribeResponse>> findValidSubscriptionsByShopId(
            @Parameter(description = "店铺ID", required = true, example = "12345")
            @PathVariable Long shopId) {
        log.info("收到根据店铺ID查询有效订购记录请求: shopId={}", shopId);

        try {
            // 参数校验
            if (shopId == null) {
                return Result.error("店铺ID不能为空");
            }

            List<ShopSubscribeResponse> responses = shopSubscribeService.findValidSubscriptionsByShopId(shopId);

            log.info("根据店铺ID查询有效订购记录成功: shopId={}, 数量={}", shopId, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("根据店铺ID查询有效订购记录失败: shopId={}", shopId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询即将过期的订购记录
     */
    @GetMapping("/expiring")
    @Operation(summary = "查询即将过期的订购记录", description = "查询指定天数内即将过期的订购记录")
    public Result<List<ShopSubscribeResponse>> findExpiringSubscriptions(
            @Parameter(description = "天数", required = true, example = "7")
            @RequestParam Integer days) {
        log.info("收到查询即将过期订购记录请求: days={}", days);

        try {
            // 参数校验
            if (days == null || days < 0) {
                return Result.error("天数参数无效");
            }

            List<ShopSubscribeResponse> responses = shopSubscribeService.findExpiringSubscriptions(days);

            log.info("查询即将过期订购记录成功: days={}, 数量={}", days, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("查询即将过期订购记录失败: days={}", days, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据买家pin查询订购记录
     */
    @GetMapping("/buyer/{buyer}")
    @Operation(summary = "根据买家pin查询订购记录", description = "根据买家pin查询订购记录列表")
    public Result<List<ShopSubscribeResponse>> findByBuyer(
            @Parameter(description = "买家pin", required = true, example = "buyer123")
            @PathVariable String buyer) {
        log.info("收到根据买家pin查询订购记录请求: buyer={}", buyer);

        try {
            // 参数校验
            if (buyer == null || buyer.trim().isEmpty()) {
                return Result.error("买家pin不能为空");
            }

            List<ShopSubscribeResponse> responses = shopSubscribeService.findByBuyer(buyer);

            log.info("根据买家pin查询订购记录成功: buyer={}, 数量={}", buyer, responses.size());
            return Result.success("查询成功", responses);
        } catch (Exception e) {
            log.error("根据买家pin查询订购记录失败: buyer={}", buyer, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询店铺订购记录
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询店铺订购记录", description = "根据条件分页查询店铺订购记录")
    public Result<PageResult<ShopSubscribeResponse>> findWithPagination(@RequestBody ShopSubscribeQueryRequest request) {
        log.info("收到分页查询店铺订购记录请求: page={}, pageSize={}", request.getPage(), request.getPageSize());

        try {
            List<ShopSubscribeResponse> responses = shopSubscribeService.findWithPagination(request);
            Long total = shopSubscribeService.countByConditions(request);

            PageResult<ShopSubscribeResponse> pageResult = new PageResult<>(
                    responses, total, request.getPage(), request.getPageSize());

            log.info("分页查询店铺订购记录成功: 数量={}, 总数={}", responses.size(), total);
            return Result.success("查询成功", pageResult);
        } catch (Exception e) {
            log.error("分页查询店铺订购记录失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 统计总记录数
     */
    @GetMapping("/count")
    @Operation(summary = "统计总记录数", description = "统计店铺订购记录总数")
    public Result<Long> countTotal() {
        log.info("收到统计总记录数请求");

        try {
            Long count = shopSubscribeService.countTotal();

            log.info("统计总记录数成功: count={}", count);
            return Result.success("统计成功", count);
        } catch (Exception e) {
            log.error("统计总记录数失败", e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }
}
