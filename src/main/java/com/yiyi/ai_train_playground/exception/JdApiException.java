package com.yiyi.ai_train_playground.exception;

/**
 * 京东API异常
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
public class JdApiException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private String errorMsg;
    
    public JdApiException() {
        super();
    }
    
    public JdApiException(String message) {
        super(message);
        this.errorMsg = message;
    }
    
    public JdApiException(String errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }
    
    public JdApiException(String message, Throwable cause) {
        super(message, cause);
        this.errorMsg = message;
    }
    
    public JdApiException(Throwable cause) {
        super(cause);
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public String getErrorMsg() {
        return errorMsg;
    }
    
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}