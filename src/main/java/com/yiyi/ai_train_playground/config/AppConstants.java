package com.yiyi.ai_train_playground.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix="app")
@Data
public final class AppConstants {

	public static String APP_KEY ="58EC57CE1D6C6B997519BA25E73A7228";
	public static String APP_SECRET =  "dbff743f33fd4a46bfa3399cf189e252";
	public static String REDIRECT_URL = "http://enterprise.yiyiai.com:4173/api/v2/yiyicallback";
	public static String TOKEN_OAUTH_URL = "https://oauth.jd.com/oauth/token";

//	public static String SERVER_ITEM_CODE;
//	public static String SERVER_PIN;
	public static String SERVER_URL = "https://api.jd.com/routerjson";
//	public static String SERVER_URL_DEV;

	public static String SERVER_URL_LOG = "https://api-log.jd.com/routerjson";

	private String serverUrlLog;
	private String appKey;
	private String appSecret;
	private String redirectUrl;
	private String tokenOauthUrl;
	private String serverItemCode;
	private String serverPin;
	private String serverUrl;
	private String serverUrlDev;

}
