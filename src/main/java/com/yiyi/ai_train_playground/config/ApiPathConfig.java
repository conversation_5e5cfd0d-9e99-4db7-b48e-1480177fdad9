package com.yiyi.ai_train_playground.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * API路径配置类
 * 用于管理不同程序的API路径前缀
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "api.path")
public class ApiPathConfig {
    
    /**
     * 内部API路径前缀
     */
    private String internal = "/api";
    
    /**
     * 外部API路径前缀
     */
    private String external = "/external/api";
    
    /**
     * V1版本API路径前缀
     */
    private String v1 = "/api/v1";
    
    /**
     * V2版本API路径前缀
     */
    private String v2 = "/api/v2";
    
    /**
     * 移动端API路径前缀
     */
    private String mobile = "/mobile/api";
    
    /**
     * 管理端API路径前缀
     */
    private String admin = "/admin/api";
    
    /**
     * 第三方集成API路径前缀
     */
    private String integration = "/integration/api";
}
