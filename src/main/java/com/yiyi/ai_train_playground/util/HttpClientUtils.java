package com.yiyi.ai_train_playground.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * HTTP客户端工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
public class HttpClientUtils {
    
    private static final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param params 请求参数
     * @param cookies Cookie信息
     * @param isJson 是否为JSON格式
     * @return 响应结果
     * @throws Exception 请求异常
     */
    public static String post(String url, Map<String, String> headers, Map<String, String> params, 
                             Map<String, String> cookies, boolean isJson) throws Exception {
        try {
            log.info("发送POST请求, url: {}, params: {}", url, params);
            
            HttpHeaders httpHeaders = new HttpHeaders();
            
            // 设置Content-Type
            if (isJson) {
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            } else {
                httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            }
            
            // 添加自定义请求头
            if (headers != null) {
                headers.forEach(httpHeaders::set);
            }
            
            // 添加Cookie
            if (cookies != null && !cookies.isEmpty()) {
                StringBuilder cookieHeader = new StringBuilder();
                cookies.forEach((key, value) -> {
                    if (cookieHeader.length() > 0) {
                        cookieHeader.append("; ");
                    }
                    cookieHeader.append(key).append("=").append(value);
                });
                httpHeaders.set("Cookie", cookieHeader.toString());
            }
            
            Object body;
            if (isJson) {
                body = JacksonUtils.object2json(params);
            } else {
                MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
                if (params != null) {
                    params.forEach(formParams::add);
                }
                body = formParams;
            }
            
            HttpEntity<?> requestEntity = new HttpEntity<>(body, httpHeaders);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            
            log.info("POST请求响应, status: {}, body: {}", response.getStatusCode(), response.getBody());
            
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            } else {
                throw new HttpReqException("HTTP请求失败, 状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("POST请求失败, url: {}, params: {}", url, params, e);
            throw new HttpReqException("POST请求失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应结果
     * @throws Exception 请求异常
     */
    public static String get(String url, Map<String, String> headers) throws Exception {
        try {
            log.info("发送GET请求, url: {}", url);
            
            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null) {
                headers.forEach(httpHeaders::set);
            }
            
            HttpEntity<?> requestEntity = new HttpEntity<>(httpHeaders);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            
            log.info("GET请求响应, status: {}, body: {}", response.getStatusCode(), response.getBody());
            
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            } else {
                throw new HttpReqException("HTTP请求失败, 状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("GET请求失败, url: {}", url, e);
            throw new HttpReqException("GET请求失败: " + e.getMessage(), e);
        }
    }
}