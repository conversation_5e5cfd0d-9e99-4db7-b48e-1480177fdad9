package com.yiyi.ai_train_playground.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Jackson工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
public class JacksonUtils {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * JSON字符串转换为对象
     * 
     * @param json JSON字符串
     * @param clazz 目标类型
     * @return 转换后的对象
     * @throws Exception 转换异常
     */
    public static <T> T json2pojo(String json, Class<T> clazz) throws Exception {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            log.error("JSON转换对象失败, json: {}, class: {}", json, clazz.getName(), e);
            throw new JacksonParseException("JSON转换对象失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * JSON字符串转换为Map
     * 
     * @param json JSON字符串
     * @return Map对象
     * @throws Exception 转换异常
     */
    public static Map<String, Object> json2map(String json) throws Exception {
        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.error("JSON转换Map失败, json: {}", json, e);
            throw new JacksonParseException("JSON转换Map失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 对象转换为JSON字符串
     * 
     * @param object 对象
     * @return JSON字符串
     * @throws Exception 转换异常
     */
    public static String object2json(Object object) throws Exception {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("对象转换JSON失败, object: {}", object, e);
            throw new JacksonParseException("对象转换JSON失败: " + e.getMessage(), e);
        }
    }
}