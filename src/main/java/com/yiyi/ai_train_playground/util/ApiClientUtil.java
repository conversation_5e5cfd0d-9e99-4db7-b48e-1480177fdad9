package com.yiyi.ai_train_playground.util;

import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.yiyi.ai_train_playground.config.AppConstants;
import com.yiyi.ai_train_playground.entity.JDSessionKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.*;

public class ApiClientUtil {
	private static Logger  log=LoggerFactory.getLogger(ApiClientUtil.class);
	public static JdClient getClient(String accessToken) {
		JdClient client = new DefaultJdClient(AppConstants.SERVER_URL, accessToken, AppConstants.APP_KEY, AppConstants.APP_SECRET);
		return client;
	}
	public static JdClient getLogClient(String accessToken) {
		JdClient client = new DefaultJdClient(AppConstants.SERVER_URL_LOG, accessToken, AppConstants.APP_KEY,
				AppConstants.APP_SECRET);
		return client;
	}

	public static JDSessionKey getSessionKeyByCode(String code) throws HttpReqException, JacksonParseException {
		log.info("----getSessionKeyByCode appkey:{},appsecret:{}------", AppConstants.APP_KEY, AppConstants.APP_SECRET);
		Map<String, String> props = new HashMap<String, String>();
		props.put("grant_type", "authorization_code");
		props.put("code", code);
		props.put("client_id", AppConstants.APP_KEY.trim());
		props.put("client_secret", AppConstants.APP_SECRET.trim());
		props.put("redirect_uri", AppConstants.REDIRECT_URL.trim());
		String authParamJson;
		JDSessionKey authParam=new JDSessionKey();
		
		try {
			authParamJson = HttpClientUtils.post(AppConstants.TOKEN_OAUTH_URL, null, props, null, false);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			throw new HttpReqException("京东授权失败，请售后再试！");
			
		}
		log.info("--get authParamJson:" + authParamJson);
		try {
			authParam = JacksonUtils.json2pojo(authParamJson, JDSessionKey.class);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			throw new JacksonParseException("授权用户访问失败，请咨询系统管理员");
		}
		return (JDSessionKey) authParam;
	}
	
	public static JDSessionKey getSessionKeyByRefreshToken(String refreshToken) throws HttpReqException, JacksonParseException {
		Map<String, String> props = new HashMap<String, String>();
		props.put("grant_type", "refresh_token");
		props.put("refresh_token", refreshToken);
		props.put("client_id", AppConstants.APP_KEY);
		props.put("client_secret", AppConstants.APP_SECRET);
		String authParamJson;
		JDSessionKey authParam;
		try {
			authParamJson = HttpClientUtils.post(AppConstants.TOKEN_OAUTH_URL, null, props, null, false);
		} catch (Exception e) {
			e.printStackTrace();  
			throw new HttpReqException("京东刷新SessionKey失败，请售后再试！");
			
		}
		log.info("京东刷新SessionKey--authParamJson:{}" , authParamJson);
		try {
			authParam = JacksonUtils.json2pojo(authParamJson, JDSessionKey.class);
		} catch (Exception e) {
			e.printStackTrace();
			throw new JacksonParseException("京东刷新SessionKey访问失败，请咨询系统管理员");
		}
		return  authParam;
	}
}
