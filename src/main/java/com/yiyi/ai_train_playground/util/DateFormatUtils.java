package com.yiyi.ai_train_playground.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日期格式化工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
public class DateFormatUtils {
    
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    private static ThreadLocal<SimpleDateFormat> threadLocal = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat(DATE_FORMAT);
        }
    };
    
    /**
     * 格式化日期为yyyy-MM-dd HH:mm:ss格式
     * 
     * @param date 日期对象
     * @return 格式化后的字符串
     */
    public static String formatYMdHms(Date date) {
        if (date == null) {
            return null;
        }
        return threadLocal.get().format(date);
    }
    
    /**
     * 清理ThreadLocal资源
     */
    public static void remove() {
        threadLocal.remove();
    }
}