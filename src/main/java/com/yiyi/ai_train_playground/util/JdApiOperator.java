package com.yiyi.ai_train_playground.util;

import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.seller.AuthSafService.response.findUser.AuthLogin;
import com.jd.open.api.sdk.domain.seller.AuthSafService.response.findUser.AuthLoginResult;
import com.jd.open.api.sdk.request.seller.VenderAuthFindUserRequest;
import com.jd.open.api.sdk.request.seller.VenderInfoQueryByPinRequest;
import com.jd.open.api.sdk.request.user.UserGetUserInfoByOpenIdRequest;
import com.jd.open.api.sdk.response.seller.VenderAuthFindUserResponse;
import com.jd.open.api.sdk.response.seller.VenderInfoQueryByPinResponse;
import com.jd.open.api.sdk.response.seller.VenderInfoResult;
import com.jd.open.api.sdk.response.user.UserGetUserInfoByOpenIdResponse;
import com.yiyi.ai_train_playground.entity.LoginUserParam;
import com.yiyi.ai_train_playground.entity.ShopInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 京东API操作工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
@Component
public class JdApiOperator {

    /**
     * 判断是否为主用户
     * 
     * @param sessionKey 会话密钥
     * @param pin 用户PIN
     * @return true-主用户, false-子用户
     * @throws Exception 异常
     */
    public boolean judgeIsMainUser(String sessionKey, String pin) throws Exception {
        JdClient client = ApiClientUtil.getClient(sessionKey);
        VenderAuthFindUserRequest request = new VenderAuthFindUserRequest();
        request.setPin(pin);
        
        VenderAuthFindUserResponse response = null;
        
        // 重试3次
        for (int i = 0; i < 3; i++) {
            try {
                response = client.execute(request);
                if (response != null && "0".equals(response.getCode())) {
                    break;
                } else {
                    if (i == 2) {
                        log.error("judgeIsMainUser response msg: {}", response == null ? "" : response.getMsg());
                        throw new Exception("识别主子pin失败: " + (response == null ? "" : response.getMsg()));
                    }
                }
            } catch (Exception e) {
                if (i == 2) {
                    log.error("judgeIsMainUser error msg: {}", e.getMessage(), e);
                    throw e;
                }
            }
        }
        
        if (response != null) {
            AuthLoginResult result = response.getResult();
            if (result != null) {
                AuthLogin login = result.getAuthLogin();
                if (login != null) {
                    // UserType: 1 主账号；0 子账号
                    return login.getUserType() == 1;
                }
            }
        }
        return false;
    }

    /**
     * 根据OpenId获取用户昵称
     * 
     * @param openId 开放ID
     * @param sessionKey 会话密钥
     * @return 用户昵称
     * @throws Exception 异常
     */
    public String getUserNickByOpenId(String openId, String sessionKey) throws Exception {
        JdClient client = ApiClientUtil.getClient(sessionKey);
        UserGetUserInfoByOpenIdRequest request = new UserGetUserInfoByOpenIdRequest();
        request.setOpenId(openId);
        
        // 设置时间戳
        try {
            request.setTimestamp(DateFormatUtils.formatYMdHms(new java.util.Date()));
        } catch (Exception e) {
            log.warn("设置时间戳失败", e);
        }
        
        UserGetUserInfoByOpenIdResponse response = null;
        
        // 重试3次
        for (int i = 0; i < 3; i++) {
            try {
                response = client.execute(request);
                if (response != null && "0".equals(response.getCode())) {
                    break;
                } else {
                    if (i == 2) {
                        log.error("getUserNickByOpenId response msg: {}", response == null ? "" : response.getMsg());
                        throw new Exception("获取用户昵称失败: " + (response == null ? "" : response.getMsg()));
                    }
                }
            } catch (Exception e) {
                if (i == 2) {
                    log.error("getUserNickByOpenId error msg: {}", e.getMessage(), e);
                    throw e;
                }
            }
        }
        
        if (response != null && response.getGetuserinfobyappidandopenidResult() != null 
                && response.getGetuserinfobyappidandopenidResult().getData() != null) {
            return response.getGetuserinfobyappidandopenidResult().getData().getNickName();
        }
        
        return null;
    }

    /**
     * 根据PIN获取店铺信息
     * 
     * @param sellerNick 用户PIN(卖家昵称)
     * @param sessionKey 会话密钥
     * @return 店铺信息
     * @throws Exception 异常
     */
    public ShopInfoDO getShopInfoByPin(String sellerNick, String sessionKey) throws Exception {
        JdClient client = ApiClientUtil.getClient(sessionKey);
        VenderInfoQueryByPinRequest request = new VenderInfoQueryByPinRequest();
        request.setExtJsonParam(sellerNick);

        
        VenderInfoQueryByPinResponse response = null;
        
        // 重试3次
        for (int i = 0; i < 3; i++) {
            try {
                response = client.execute(request);
                if (response != null && "0".equals(response.getCode())) {
                    break;
                } else {
                    if (i == 2) {
                        log.error("getShopInfoByPin response msg: {}", response == null ? "" : response.getMsg());
                        throw new Exception("获取店铺信息失败: " + (response == null ? "" : response.getMsg()));
                    }
                }
            } catch (Exception e) {
                if (i == 2) {
                    log.error("getShopInfoByPin error msg: {}", e.getMessage(), e);
                    throw e;
                }
            }
        }
        
        if (response != null && StringUtils.isNotBlank(response.getCode()) &&"0".equals(response.getCode())) {
            VenderInfoResult shopInfo=response.getVenderInfoResult();

            if(shopInfo != null){
                ShopInfoDO shopInfoDO = new ShopInfoDO();
                shopInfoDO.setVenderId(shopInfo.getVenderId());
                shopInfoDO.setShopId(shopInfo.getShopId());
                shopInfoDO.setShopName(shopInfo.getShopName());
                shopInfoDO.setColType(shopInfo.getColType());

                // 根据colType判断店铺类型: 0-POP店铺, 1-自营店铺
                shopInfoDO.setVenderType(shopInfo.getColType() != null && shopInfo.getColType() == 1 ? 1 : 0);
                return shopInfoDO;
            }

        }
        
        return null;
    }
}