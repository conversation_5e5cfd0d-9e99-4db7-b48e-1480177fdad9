package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.ShopSubscribeQueryRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeResponse;
import com.yiyi.ai_train_playground.entity.ShopSubscribe;
import com.yiyi.ai_train_playground.mapper.ShopSubscribeMapper;
import com.yiyi.ai_train_playground.service.ShopSubscribeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺订购服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopSubscribeServiceImpl implements ShopSubscribeService {
    
    private final ShopSubscribeMapper shopSubscribeMapper;
    
    @Override
    @Transactional
    public boolean create(ShopSubscribeRequest request) {
        if (request == null) {
            log.warn("店铺订购请求不能为空");
            return false;
        }
        
        try {
            log.info("创建店铺订购记录: shopId={}, shopType={}", request.getShopId(), request.getShopType());
            
            ShopSubscribe entity = convertToEntity(request);
            int result = shopSubscribeMapper.insert(entity);
            boolean success = result > 0;
            
            if (success) {
                log.info("创建店铺订购记录成功: id={}", entity.getId());
            } else {
                log.warn("创建店铺订购记录失败");
            }
            
            return success;
        } catch (Exception e) {
            log.error("创建店铺订购记录失败: shopId={}, shopType={}", request.getShopId(), request.getShopType(), e);
            throw new RuntimeException("创建店铺订购记录失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public boolean deleteById(Long id) {
        if (id == null) {
            log.warn("ID不能为空");
            return false;
        }
        
        try {
            log.info("删除店铺订购记录: id={}", id);
            
            int result = shopSubscribeMapper.deleteById(id);
            boolean success = result > 0;
            
            if (success) {
                log.info("删除店铺订购记录成功: id={}", id);
            } else {
                log.warn("删除店铺订购记录失败，记录可能不存在: id={}", id);
            }
            
            return success;
        } catch (Exception e) {
            log.error("删除店铺订购记录失败: id={}", id, e);
            throw new RuntimeException("删除店铺订购记录失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public boolean deleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("ID列表不能为空");
            return false;
        }
        
        try {
            log.info("批量删除店铺订购记录: ids={}", ids);
            
            int result = shopSubscribeMapper.deleteByIds(ids);
            boolean success = result > 0;
            
            if (success) {
                log.info("批量删除店铺订购记录成功: 删除数量={}", result);
            } else {
                log.warn("批量删除店铺订购记录失败");
            }
            
            return success;
        } catch (Exception e) {
            log.error("批量删除店铺订购记录失败: ids={}", ids, e);
            throw new RuntimeException("批量删除店铺订购记录失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public boolean updateById(Long id, ShopSubscribeRequest request) {
        if (id == null || request == null) {
            log.warn("ID和请求参数不能为空");
            return false;
        }
        
        try {
            log.info("更新店铺订购记录: id={}, shopId={}", id, request.getShopId());
            
            ShopSubscribe entity = convertToEntity(request);
            entity.setId(id);
            
            int result = shopSubscribeMapper.updateById(entity);
            boolean success = result > 0;
            
            if (success) {
                log.info("更新店铺订购记录成功: id={}", id);
            } else {
                log.warn("更新店铺订购记录失败，记录可能不存在: id={}", id);
            }
            
            return success;
        } catch (Exception e) {
            log.error("更新店铺订购记录失败: id={}", id, e);
            throw new RuntimeException("更新店铺订购记录失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public ShopSubscribeResponse findById(Long id) {
        if (id == null) {
            log.warn("ID不能为空");
            return null;
        }
        
        try {
            log.debug("根据ID查询店铺订购记录: id={}", id);
            
            ShopSubscribe entity = shopSubscribeMapper.selectById(id);
            ShopSubscribeResponse response = ShopSubscribeResponse.fromEntity(entity);
            
            log.debug("根据ID查询店铺订购记录完成: id={}, 找到={}", id, entity != null);
            return response;
        } catch (Exception e) {
            log.error("根据ID查询店铺订购记录失败: id={}", id, e);
            return null;
        }
    }
    
    @Override
    public List<ShopSubscribeResponse> findAll() {
        try {
            log.debug("查询所有店铺订购记录");
            
            List<ShopSubscribe> entities = shopSubscribeMapper.selectAll();
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());
            
            log.debug("查询所有店铺订购记录完成: 数量={}", responses.size());
            return responses;
        } catch (Exception e) {
            log.error("查询所有店铺订购记录失败", e);
            return List.of();
        }
    }
    
    @Override
    public List<ShopSubscribeResponse> findByShopId(Long shopId) {
        if (shopId == null) {
            log.warn("店铺ID不能为空");
            return List.of();
        }
        
        try {
            log.debug("根据店铺ID查询订购记录: shopId={}", shopId);
            
            List<ShopSubscribe> entities = shopSubscribeMapper.selectByShopId(shopId);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());
            
            log.debug("根据店铺ID查询订购记录完成: shopId={}, 数量={}", shopId, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据店铺ID查询订购记录失败: shopId={}", shopId, e);
            return List.of();
        }
    }
    
    @Override
    public List<ShopSubscribeResponse> findByShopIdAndType(Long shopId, Integer shopType) {
        if (shopId == null || shopType == null) {
            log.warn("店铺ID和店铺类型不能为空");
            return List.of();
        }
        
        try {
            log.debug("根据店铺ID和类型查询订购记录: shopId={}, shopType={}", shopId, shopType);
            
            List<ShopSubscribe> entities = shopSubscribeMapper.selectByShopIdAndType(shopId, shopType);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());
            
            log.debug("根据店铺ID和类型查询订购记录完成: shopId={}, shopType={}, 数量={}", 
                    shopId, shopType, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据店铺ID和类型查询订购记录失败: shopId={}, shopType={}", shopId, shopType, e);
            return List.of();
        }
    }
    
    @Override
    public List<ShopSubscribeResponse> findByShopType(Integer shopType) {
        if (shopType == null) {
            log.warn("店铺类型不能为空");
            return List.of();
        }

        try {
            log.debug("根据店铺类型查询订购记录: shopType={}", shopType);

            List<ShopSubscribe> entities = shopSubscribeMapper.selectByShopType(shopType);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("根据店铺类型查询订购记录完成: shopType={}, 数量={}", shopType, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据店铺类型查询订购记录失败: shopType={}", shopType, e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findByStatus(Integer status) {
        if (status == null) {
            log.warn("订购状态不能为空");
            return List.of();
        }

        try {
            log.debug("根据订购状态查询记录: status={}", status);

            List<ShopSubscribe> entities = shopSubscribeMapper.selectByStatus(status);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("根据订购状态查询记录完成: status={}, 数量={}", status, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据订购状态查询记录失败: status={}", status, e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findByOrderStatus(Integer orderStatus) {
        if (orderStatus == null) {
            log.warn("订单状态不能为空");
            return List.of();
        }

        try {
            log.debug("根据订单状态查询记录: orderStatus={}", orderStatus);

            List<ShopSubscribe> entities = shopSubscribeMapper.selectByOrderStatus(orderStatus);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("根据订单状态查询记录完成: orderStatus={}, 数量={}", orderStatus, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据订单状态查询记录失败: orderStatus={}", orderStatus, e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findValidSubscriptions() {
        try {
            log.debug("查询有效的订购记录");

            List<ShopSubscribe> entities = shopSubscribeMapper.selectValidSubscriptions();
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("查询有效的订购记录完成: 数量={}", responses.size());
            return responses;
        } catch (Exception e) {
            log.error("查询有效的订购记录失败", e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findValidSubscriptionsByShopId(Long shopId) {
        if (shopId == null) {
            log.warn("店铺ID不能为空");
            return List.of();
        }

        try {
            log.debug("根据店铺ID查询有效的订购记录: shopId={}", shopId);

            List<ShopSubscribe> entities = shopSubscribeMapper.selectValidSubscriptionsByShopId(shopId);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("根据店铺ID查询有效的订购记录完成: shopId={}, 数量={}", shopId, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据店铺ID查询有效的订购记录失败: shopId={}", shopId, e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findExpiringSubscriptions(Integer days) {
        if (days == null || days < 0) {
            log.warn("天数参数无效: days={}", days);
            return List.of();
        }

        try {
            log.debug("查询即将过期的订购记录: days={}", days);

            List<ShopSubscribe> entities = shopSubscribeMapper.selectExpiringSubscriptions(days);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("查询即将过期的订购记录完成: days={}, 数量={}", days, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("查询即将过期的订购记录失败: days={}", days, e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findByBuyer(String buyer) {
        if (buyer == null || buyer.trim().isEmpty()) {
            log.warn("买家pin不能为空");
            return List.of();
        }

        try {
            log.debug("根据买家pin查询订购记录: buyer={}", buyer);

            List<ShopSubscribe> entities = shopSubscribeMapper.selectByBuyer(buyer);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("根据买家pin查询订购记录完成: buyer={}, 数量={}", buyer, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据买家pin查询订购记录失败: buyer={}", buyer, e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findByItemCode(String itemCode) {
        if (itemCode == null || itemCode.trim().isEmpty()) {
            log.warn("服务编码不能为空");
            return List.of();
        }

        try {
            log.debug("根据服务编码查询订购记录: itemCode={}", itemCode);

            List<ShopSubscribe> entities = shopSubscribeMapper.selectByItemCode(itemCode);
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("根据服务编码查询订购记录完成: itemCode={}, 数量={}", itemCode, responses.size());
            return responses;
        } catch (Exception e) {
            log.error("根据服务编码查询订购记录失败: itemCode={}", itemCode, e);
            return List.of();
        }
    }

    @Override
    public List<ShopSubscribeResponse> findWithPagination(ShopSubscribeQueryRequest request) {
        if (request == null) {
            log.warn("查询请求不能为空");
            return List.of();
        }

        try {
            log.debug("分页查询店铺订购记录: page={}, pageSize={}", request.getPage(), request.getPageSize());

            List<ShopSubscribe> entities = shopSubscribeMapper.selectWithPagination(
                    request.getOffset(), request.getPageSize());
            List<ShopSubscribeResponse> responses = entities.stream()
                    .map(ShopSubscribeResponse::fromEntity)
                    .collect(Collectors.toList());

            log.debug("分页查询店铺订购记录完成: 数量={}", responses.size());
            return responses;
        } catch (Exception e) {
            log.error("分页查询店铺订购记录失败", e);
            return List.of();
        }
    }

    @Override
    public Long countTotal() {
        try {
            log.debug("统计总记录数");

            Long count = shopSubscribeMapper.countTotal();

            log.debug("统计总记录数完成: count={}", count);
            return count;
        } catch (Exception e) {
            log.error("统计总记录数失败", e);
            return 0L;
        }
    }

    @Override
    public Long countByConditions(ShopSubscribeQueryRequest request) {
        if (request == null) {
            return countTotal();
        }

        try {
            log.debug("根据条件统计记录数: shopId={}, shopType={}", request.getShopId(), request.getShopType());

            Long count = shopSubscribeMapper.countByConditions(
                    request.getShopId(), request.getShopType(),
                    request.getStatus(), request.getOrderStatus());

            log.debug("根据条件统计记录数完成: count={}", count);
            return count;
        } catch (Exception e) {
            log.error("根据条件统计记录数失败", e);
            return 0L;
        }
    }

    @Override
    public boolean existsById(Long id) {
        if (id == null) {
            return false;
        }

        try {
            ShopSubscribe entity = shopSubscribeMapper.selectById(id);
            return entity != null;
        } catch (Exception e) {
            log.error("检查记录是否存在失败: id={}", id, e);
            return false;
        }
    }

    /**
     * 将请求DTO转换为实体对象
     */
    private ShopSubscribe convertToEntity(ShopSubscribeRequest request) {
        ShopSubscribe entity = new ShopSubscribe();
        entity.setShopId(request.getShopId());
        entity.setShopType(request.getShopType());
        entity.setStartTime(request.getStartTime());
        entity.setEndTime(request.getEndTime());
        entity.setStatus(request.getStatus());
        entity.setVersion(request.getVersion());
        entity.setOrderCycle(request.getOrderCycle());
        entity.setBuyer(request.getBuyer());
        entity.setNickName(request.getNickName());
        entity.setServiceName(request.getServiceName());
        entity.setItemCode(request.getItemCode());
        entity.setOrderStatus(request.getOrderStatus());
        return entity;
    }
}
