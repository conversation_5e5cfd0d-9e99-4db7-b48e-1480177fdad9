package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.ShopSubscribeQueryRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeResponse;
import com.yiyi.ai_train_playground.entity.ShopSubscribe;

import java.util.List;

/**
 * 店铺订购服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public interface ShopSubscribeService {
    
    /**
     * 创建店铺订购记录
     * 
     * @param request 店铺订购请求
     * @return 创建成功返回true，失败返回false
     */
    boolean create(ShopSubscribeRequest request);
    
    /**
     * 根据ID删除店铺订购记录
     * 
     * @param id 主键ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteById(Long id);
    
    /**
     * 批量删除店铺订购记录
     * 
     * @param ids 主键ID列表
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteByIds(List<Long> ids);
    
    /**
     * 更新店铺订购记录
     * 
     * @param id 主键ID
     * @param request 店铺订购请求
     * @return 更新成功返回true，失败返回false
     */
    boolean updateById(Long id, ShopSubscribeRequest request);
    
    /**
     * 根据ID查询店铺订购记录
     * 
     * @param id 主键ID
     * @return 店铺订购响应DTO，如果不存在返回null
     */
    ShopSubscribeResponse findById(Long id);
    
    /**
     * 查询所有店铺订购记录
     * 
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findAll();
    
    /**
     * 根据店铺ID查询订购记录
     * 
     * @param shopId 店铺ID
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findByShopId(Long shopId);
    
    /**
     * 根据店铺ID和店铺类型查询订购记录
     * 
     * @param shopId 店铺ID
     * @param shopType 店铺类型
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findByShopIdAndType(Long shopId, Integer shopType);
    
    /**
     * 根据店铺类型查询订购记录
     * 
     * @param shopType 店铺类型
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findByShopType(Integer shopType);
    
    /**
     * 根据订购状态查询记录
     * 
     * @param status 订购状态
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findByStatus(Integer status);
    
    /**
     * 根据订单状态查询记录
     * 
     * @param orderStatus 订单状态
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findByOrderStatus(Integer orderStatus);
    
    /**
     * 查询有效的订购记录（未过期）
     * 
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findValidSubscriptions();
    
    /**
     * 根据店铺ID查询有效的订购记录
     * 
     * @param shopId 店铺ID
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findValidSubscriptionsByShopId(Long shopId);
    
    /**
     * 查询即将过期的订购记录（指定天数内过期）
     * 
     * @param days 天数
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findExpiringSubscriptions(Integer days);
    
    /**
     * 根据买家pin查询订购记录
     * 
     * @param buyer 买家pin
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findByBuyer(String buyer);
    
    /**
     * 根据服务编码查询订购记录
     * 
     * @param itemCode 服务编码
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findByItemCode(String itemCode);
    
    /**
     * 分页查询店铺订购记录
     * 
     * @param request 查询请求
     * @return 店铺订购响应DTO列表
     */
    List<ShopSubscribeResponse> findWithPagination(ShopSubscribeQueryRequest request);
    
    /**
     * 统计总记录数
     * 
     * @return 总记录数
     */
    Long countTotal();
    
    /**
     * 根据条件统计记录数
     * 
     * @param request 查询请求
     * @return 记录数
     */
    Long countByConditions(ShopSubscribeQueryRequest request);
    
    /**
     * 检查店铺订购记录是否存在
     * 
     * @param id 主键ID
     * @return 存在返回true，不存在返回false
     */
    boolean existsById(Long id);
}
