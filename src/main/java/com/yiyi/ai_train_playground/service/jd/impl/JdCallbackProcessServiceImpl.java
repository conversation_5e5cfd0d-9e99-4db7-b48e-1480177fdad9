package com.yiyi.ai_train_playground.service.jd.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yiyi.ai_train_playground.entity.AuthorizationUserInfo;
import com.yiyi.ai_train_playground.entity.JDSessionKey;
import com.yiyi.ai_train_playground.entity.LoginUserParam;
import com.yiyi.ai_train_playground.entity.ShopInfoDO;
import com.yiyi.ai_train_playground.service.jd.JdCallbackProcessService;
import com.yiyi.ai_train_playground.util.ApiClientUtil;
import com.yiyi.ai_train_playground.util.JdApiOperator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 京东回调处理服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JdCallbackProcessServiceImpl implements JdCallbackProcessService {

    private final JdApiOperator jdApiOperator;

    @Override
    public JDSessionKey getSessionKeyByCode(String code) throws Exception {
        log.info("开始通过code获取SessionKey, code: {}", code);
        
        JDSessionKey sessionKey = ApiClientUtil.getSessionKeyByCode(code);
        
        if (sessionKey == null) {
            throw new Exception("获取SessionKey失败，返回为空");
        }
        
        if (sessionKey.getCode() != null && sessionKey.getCode() == 304) {
            throw new Exception("授权失败，请重新登录");
        }
        
        if (sessionKey.getCode() != null && sessionKey.getCode() == 405) {
            throw new Exception("应用已发布到服务市场，请到服务市场订购");
        }
        
        log.info("成功获取SessionKey, uid: {}, openId: {}, expires_in: {}", 
                sessionKey.getUid(), sessionKey.getOpen_id(), sessionKey.getExpires_in());
        
        return sessionKey;
    }

    @Override
    public boolean isSubAccount(String sessionKey, String pin) throws Exception {
        log.info("判断是否为子账号, pin: {}", pin);
        
        if (!StringUtils.hasText(sessionKey) || !StringUtils.hasText(pin)) {
            throw new Exception("sessionKey或pin不能为空");
        }
        
        boolean isMainUser = jdApiOperator.judgeIsMainUser(sessionKey, pin);
        boolean isSubAccount = !isMainUser;
        
        log.info("用户类型判断结果, pin: {}, isMainUser: {}, isSubAccount: {}", 
                pin, isMainUser, isSubAccount);
        
        return isSubAccount;
    }

    @Override
    public LoginUserParam buildLoginUserParam(JDSessionKey jdSessionKey, String pin, String openId, String state) throws Exception {
        log.info("构建LoginUserParam, pin: {}, uid: {}, openId: {}", 
                pin, jdSessionKey.getUid(), openId);
        
        LoginUserParam loginUserParam = new LoginUserParam();
        
        // 基本信息
        loginUserParam.setUid(jdSessionKey.getUid());
        loginUserParam.setPin(pin);
        loginUserParam.setSessionKey(jdSessionKey.getAccess_token());
        loginUserParam.setOpenId(openId);
        loginUserParam.setRefreshSessionKey(jdSessionKey.getRefresh_token());
        
        // 获取用户昵称
        try {
            String userNick = jdApiOperator.getUserNickByOpenId(openId, jdSessionKey.getAccess_token());
            loginUserParam.setUserNick(userNick);
            log.info("获取用户昵称成功, openId: {}, userNick: {}", openId, userNick);
        } catch (Exception e) {
            log.error("获取用户昵称失败, openId: {}, error: {}", openId, e.getMessage(), e);
            // 昵称获取失败不影响主流程，使用pin作为昵称
            loginUserParam.setUserNick(pin);
        }
        
        // 从state解析获取endDate和itemCode
        try {
            AuthorizationUserInfo user = decodeStateOfPin(state);
            if (user != null) {
                // 设置结束日期
                if (user.getEnd_date() != null) {
                    loginUserParam.setEndDate(new Date(user.getEnd_date()));
                    log.info("从state获取到结束日期: {}", new Date(user.getEnd_date()));
                }
                
                // 设置产品代码
                if (StringUtils.hasText(user.getItem_code())) {
                    loginUserParam.setItemCode(user.getItem_code());
                    log.info("从state获取到产品代码: {}", user.getItem_code());
                }
            }
        } catch (Exception e) {
            log.warn("从state解析endDate和itemCode失败，使用默认值", e);
            // 解析失败，使用SessionKey的过期时间作为fallback
            if (jdSessionKey.getExpires_in() != null) {
                long expireTime = System.currentTimeMillis() + jdSessionKey.getExpires_in() * 1000;
                loginUserParam.setEndDate(new Date(expireTime));
            }
        }
        
        log.info("LoginUserParam构建完成: {}", loginUserParam);
        
        return loginUserParam;
    }

    @Override
    public ShopInfoDO buildShopInfo(String pin, String sessionKey) throws Exception {
        log.info("构建ShopInfo, pin: {}", pin);
        
        if (!StringUtils.hasText(pin) || !StringUtils.hasText(sessionKey)) {
            throw new Exception("pin或sessionKey不能为空");
        }
        
        ShopInfoDO shopInfo = jdApiOperator.getShopInfoByPin(pin, sessionKey);
        
        if (shopInfo == null) {
            throw new Exception("获取店铺信息失败");
        }
        
        log.info("ShopInfo构建完成: {}", shopInfo);
        
        return shopInfo;
    }

    @Override
    public String getPinFromState(String state) throws Exception {
        log.info("开始解析state获取PIN, state: {}", state);
        
        if (!StringUtils.hasText(state)) {
            throw new Exception("state参数不能为空");
        }
        
        AuthorizationUserInfo user = decodeStateOfPin(state);
        
        if (user == null) {
            throw new Exception("解析state失败，无法获取用户信息");
        }
        
        if (!StringUtils.hasText(user.getUser_name())) {
            throw new Exception("从state中获取的用户名为空");
        }
        
        String pin = user.getUser_name().toLowerCase();
        log.info("成功从state解析获取PIN: {}", pin);
        
        return pin;
    }
    
    /**
     * 解析state参数获取用户信息
     * 
     * @param state 状态参数
     * @return 授权用户信息
     * @throws Exception 异常
     */
    private AuthorizationUserInfo decodeStateOfPin(String state) throws Exception {
        AuthorizationUserInfo user = null;
        
        if (StringUtils.hasText(state)) {
            String states = "";
            // 处理空格，替换为+号
            Pattern p = Pattern.compile("\\s");
            Matcher m = p.matcher(state);
            if (m.find()) {
                states = m.replaceAll("+");
            } else {
                states = state;
            }
            
            log.info("转换后states: {}", states);
            
            // Base64解码
            states = new String(Base64.getDecoder().decode(states), "UTF-8");
            log.info("Base64解码后states: {}", states);
            
            // JSON解析
            Map<String, AuthorizationUserInfo> userMap = JSONObject.parseObject(states, new TypeReference<Map<String, AuthorizationUserInfo>>() {});
            user = userMap.get("jos_parameters");
            
            log.info("解析得到用户信息: {}", user);
        }
        
        return user;
    }
}