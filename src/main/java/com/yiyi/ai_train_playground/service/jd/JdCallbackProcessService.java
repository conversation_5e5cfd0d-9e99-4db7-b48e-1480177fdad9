package com.yiyi.ai_train_playground.service.jd;

import com.yiyi.ai_train_playground.entity.JDSessionKey;
import com.yiyi.ai_train_playground.entity.LoginUserParam;
import com.yiyi.ai_train_playground.entity.ShopInfoDO;

/**
 * 京东回调处理服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
public interface JdCallbackProcessService {

    /**
     * 根据code获取京东SessionKey
     * 
     * @param code 授权码
     * @return JDSessionKey对象
     * @throws Exception 异常
     */
    JDSessionKey getSessionKeyByCode(String code) throws Exception;

    /**
     * 判断是否为子账号
     * 
     * @param sessionKey 会话密钥
     * @param pin 用户PIN
     * @return true-子账号, false-主账号
     * @throws Exception 异常
     */
    boolean isSubAccount(String sessionKey, String pin) throws Exception;

    /**
     * 构建登录用户参数
     * 
     * @param sessionKey 京东SessionKey对象
     * @param pin 用户PIN
     * @param openId 开放ID
     * @param state 状态参数(用于获取endDate和itemCode)
     * @return LoginUserParam对象
     * @throws Exception 异常
     */
    LoginUserParam buildLoginUserParam(JDSessionKey sessionKey, String pin, String openId, String state) throws Exception;

    /**
     * 构建店铺信息
     * 
     * @param pin 用户PIN
     * @param sessionKey 会话密钥
     * @return ShopInfoDO对象
     * @throws Exception 异常
     */
    ShopInfoDO buildShopInfo(String pin, String sessionKey) throws Exception;

    /**
     * 从state参数解析获取用户PIN
     * 
     * @param state 状态参数
     * @return 用户PIN
     * @throws Exception 异常
     */
    String getPinFromState(String state) throws Exception;
}