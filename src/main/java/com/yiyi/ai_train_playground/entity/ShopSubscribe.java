package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 店铺订购表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class ShopSubscribe {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺类型,0:京东，1：淘宝，2：抖店，3: 网盟入口
     */
    private Integer shopType;
    
    /**
     * 购买时间
     */
    private LocalDateTime startTime;
    
    /**
     * 到期时间
     */
    private LocalDateTime endTime;
    
    /**
     * 订购状态（1：订购、2：续费、3：升级）
     */
    private Integer status;
    
    /**
     * 订购版本 eg:'不限账号'
     */
    private String version;
    
    /**
     * 订购期限（eg:180）
     */
    private Integer orderCycle;
    
    /**
     * 买家pin
     */
    private String buyer;
    
    /**
     * 买家昵称
     */
    private String nickName;
    
    /**
     * 服务名称
     */
    private String serviceName;
    
    /**
     * 服务编码
     */
    private String itemCode;
    
    /**
     * 订单状态（1：等待付款、2：等待付款确认、4：订单完成、8：取消完成、16：退款中、64：退款完成）
     */
    private Integer orderStatus;
    
    /**
     * 获取店铺类型名称
     */
    public String getShopTypeName() {
        if (shopType == null) {
            return "未知";
        }
        switch (shopType) {
            case 0:
                return "京东";
            case 1:
                return "淘宝";
            case 2:
                return "抖店";
            case 3:
                return "网盟入口";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取订购状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "订购";
            case 2:
                return "续费";
            case 3:
                return "升级";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取订单状态名称
     */
    public String getOrderStatusName() {
        if (orderStatus == null) {
            return "未知";
        }
        switch (orderStatus) {
            case 1:
                return "等待付款";
            case 2:
                return "等待付款确认";
            case 4:
                return "订单完成";
            case 8:
                return "取消完成";
            case 16:
                return "退款中";
            case 64:
                return "退款完成";
            default:
                return "未知";
        }
    }
    
    /**
     * 判断订购是否有效（未过期）
     */
    public boolean isValid() {
        if (endTime == null) {
            return false;
        }
        return LocalDateTime.now().isBefore(endTime);
    }
}
