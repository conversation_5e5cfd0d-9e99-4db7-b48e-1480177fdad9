package com.yiyi.ai_train_playground.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 授权用户信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AuthorizationUserInfo {
    
    /**
     * 用户PIN
     */
    @JSONField(name = "user_name")
    private String user_name;
    
    /**
     * 结束日期
     */
    @JSONField(name = "end_date")
    private Long end_date;
    
    /**
     * 产品代码
     */
    @JSONField(name = "item_code")
    private String item_code;
    
    @Override
    public String toString() {
        return "AuthorizationUserInfo [user_name=" + user_name + ", end_date=" + end_date + ", item_code=" + item_code + "]";
    }
}