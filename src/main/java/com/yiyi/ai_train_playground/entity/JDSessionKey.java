
package com.yiyi.ai_train_playground.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JDSessionKey implements Serializable {
	private static final long serialVersionUID = 6824869560014479023L;
	private String access_token;
	private Long expires_in;// 失效时间（从当前时间算起，单位：秒）
	private Integer code;// 返回的代码
	private String refresh_token;
	private String scope;
	private Long time;// 授权的时间点（UNIX时间戳，单位：毫秒）
	private String token_type;// token类型（暂无意义）
	private String uid;// 授权用户对应的京东ID
	private String user_nick;// 京东授权昵称
	private String open_id;
	private String xId;
}
