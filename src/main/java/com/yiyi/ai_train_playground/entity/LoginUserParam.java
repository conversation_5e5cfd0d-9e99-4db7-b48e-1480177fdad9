package com.yiyi.ai_train_playground.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 登录用户参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Data
public class LoginUserParam {

    /**
     * 京东用户ID
     */
    private String uid;
    
    /**
     * 用户昵称
     */
    private String userNick;
    
    /**
     * 用户PIN
     */
    private String pin;
    
    /**
     * 会话密钥
     */
    private String sessionKey;

    /**
     * 结束日期
     */
    private Date endDate;
    
    /**
     * 产品代码
     */
    private String itemCode;

    /**
     * 开放ID
     */
    private String openId;

    /**
     * 商家类型 0:POP店铺 1:自营店铺
     */
    private Integer venderType;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 刷新令牌
     */
    private String refreshSessionKey;

    /**
     * 店铺ID
     */
    private Long shopId;
}