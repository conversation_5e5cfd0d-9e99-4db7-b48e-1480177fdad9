package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.ShopSubscribe;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 店铺订购表Mapper接口
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface ShopSubscribeMapper {
    
    /**
     * 插入店铺订购记录
     * @param shopSubscribe 店铺订购实体
     * @return 影响行数
     */
    int insert(ShopSubscribe shopSubscribe);
    
    /**
     * 根据ID删除店铺订购记录
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 批量删除店铺订购记录
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int deleteByIds(@Param("ids") List<Long> ids);
    
    /**
     * 更新店铺订购记录
     * @param shopSubscribe 店铺订购实体
     * @return 影响行数
     */
    int updateById(ShopSubscribe shopSubscribe);
    
    /**
     * 根据ID查询店铺订购记录
     * @param id 主键ID
     * @return 店铺订购实体
     */
    ShopSubscribe selectById(@Param("id") Long id);
    
    /**
     * 查询所有店铺订购记录
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectAll();
    
    /**
     * 根据店铺ID查询订购记录
     * @param shopId 店铺ID
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectByShopId(@Param("shopId") Long shopId);
    
    /**
     * 根据店铺ID和店铺类型查询订购记录
     * @param shopId 店铺ID
     * @param shopType 店铺类型
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectByShopIdAndType(@Param("shopId") Long shopId, @Param("shopType") Integer shopType);
    
    /**
     * 根据店铺类型查询订购记录
     * @param shopType 店铺类型
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectByShopType(@Param("shopType") Integer shopType);
    
    /**
     * 根据订购状态查询记录
     * @param status 订购状态
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectByStatus(@Param("status") Integer status);
    
    /**
     * 根据订单状态查询记录
     * @param orderStatus 订单状态
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectByOrderStatus(@Param("orderStatus") Integer orderStatus);
    
    /**
     * 查询有效的订购记录（未过期）
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectValidSubscriptions();
    
    /**
     * 根据店铺ID查询有效的订购记录
     * @param shopId 店铺ID
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectValidSubscriptionsByShopId(@Param("shopId") Long shopId);
    
    /**
     * 查询即将过期的订购记录（指定天数内过期）
     * @param days 天数
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectExpiringSubscriptions(@Param("days") Integer days);
    
    /**
     * 根据买家pin查询订购记录
     * @param buyer 买家pin
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectByBuyer(@Param("buyer") String buyer);
    
    /**
     * 根据服务编码查询订购记录
     * @param itemCode 服务编码
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectByItemCode(@Param("itemCode") String itemCode);
    
    /**
     * 分页查询店铺订购记录
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 店铺订购实体列表
     */
    List<ShopSubscribe> selectWithPagination(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 统计总记录数
     * @return 总记录数
     */
    Long countTotal();
    
    /**
     * 根据条件统计记录数
     * @param shopId 店铺ID（可选）
     * @param shopType 店铺类型（可选）
     * @param status 订购状态（可选）
     * @param orderStatus 订单状态（可选）
     * @return 记录数
     */
    Long countByConditions(@Param("shopId") Long shopId, 
                          @Param("shopType") Integer shopType,
                          @Param("status") Integer status, 
                          @Param("orderStatus") Integer orderStatus);
}
