<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.ShopSubscribeMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.ShopSubscribe">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_type" property="shopType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="version" property="version"/>
        <result column="order_cycle" property="orderCycle"/>
        <result column="buyer" property="buyer"/>
        <result column="nick_name" property="nickName"/>
        <result column="service_name" property="serviceName"/>
        <result column="item_code" property="itemCode"/>
        <result column="order_status" property="orderStatus"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, shop_id, shop_type, start_time, end_time, status, version, 
        order_cycle, buyer, nick_name, service_name, item_code, order_status
    </sql>

    <!-- 插入店铺订购记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.ShopSubscribe" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO shop_subcribe (
            shop_id, shop_type, start_time, end_time, status, version,
            order_cycle, buyer, nick_name, service_name, item_code, order_status
        ) VALUES (
            #{shopId}, #{shopType}, #{startTime}, #{endTime}, #{status}, #{version},
            #{orderCycle}, #{buyer}, #{nickName}, #{serviceName}, #{itemCode}, #{orderStatus}
        )
    </insert>

    <!-- 根据ID删除店铺订购记录 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM shop_subcribe WHERE id = #{id}
    </delete>

    <!-- 批量删除店铺订购记录 -->
    <delete id="deleteByIds">
        DELETE FROM shop_subcribe WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新店铺订购记录 -->
    <update id="updateById" parameterType="com.yiyi.ai_train_playground.entity.ShopSubscribe">
        UPDATE shop_subcribe
        <set>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="shopType != null">shop_type = #{shopType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="version != null">version = #{version},</if>
            <if test="orderCycle != null">order_cycle = #{orderCycle},</if>
            <if test="buyer != null">buyer = #{buyer},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="orderStatus != null">order_status = #{orderStatus}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询店铺订购记录 -->
    <select id="selectById" resultMap="BaseResultMap" parameterType="long">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE id = #{id}
    </select>

    <!-- 查询所有店铺订购记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        ORDER BY id DESC
    </select>

    <!-- 根据店铺ID查询订购记录 -->
    <select id="selectByShopId" resultMap="BaseResultMap" parameterType="long">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE shop_id = #{shopId}
        ORDER BY start_time DESC
    </select>

    <!-- 根据店铺ID和店铺类型查询订购记录 -->
    <select id="selectByShopIdAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE shop_id = #{shopId} AND shop_type = #{shopType}
        ORDER BY start_time DESC
    </select>

    <!-- 根据店铺类型查询订购记录 -->
    <select id="selectByShopType" resultMap="BaseResultMap" parameterType="int">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE shop_type = #{shopType}
        ORDER BY start_time DESC
    </select>

    <!-- 根据订购状态查询记录 -->
    <select id="selectByStatus" resultMap="BaseResultMap" parameterType="int">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE status = #{status}
        ORDER BY start_time DESC
    </select>

    <!-- 根据订单状态查询记录 -->
    <select id="selectByOrderStatus" resultMap="BaseResultMap" parameterType="int">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE order_status = #{orderStatus}
        ORDER BY start_time DESC
    </select>

    <!-- 查询有效的订购记录（未过期） -->
    <select id="selectValidSubscriptions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE end_time IS NOT NULL AND end_time &gt; NOW()
        ORDER BY end_time ASC
    </select>

    <!-- 根据店铺ID查询有效的订购记录 -->
    <select id="selectValidSubscriptionsByShopId" resultMap="BaseResultMap" parameterType="long">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE shop_id = #{shopId} AND end_time IS NOT NULL AND end_time &gt; NOW()
        ORDER BY end_time ASC
    </select>

    <!-- 查询即将过期的订购记录（指定天数内过期） -->
    <select id="selectExpiringSubscriptions" resultMap="BaseResultMap" parameterType="int">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE end_time IS NOT NULL 
        AND end_time &gt; NOW() 
        AND end_time &lt;= DATE_ADD(NOW(), INTERVAL #{days} DAY)
        ORDER BY end_time ASC
    </select>

    <!-- 根据买家pin查询订购记录 -->
    <select id="selectByBuyer" resultMap="BaseResultMap" parameterType="string">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE buyer = #{buyer}
        ORDER BY start_time DESC
    </select>

    <!-- 根据服务编码查询订购记录 -->
    <select id="selectByItemCode" resultMap="BaseResultMap" parameterType="string">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        WHERE item_code = #{itemCode}
        ORDER BY start_time DESC
    </select>

    <!-- 分页查询店铺订购记录 -->
    <select id="selectWithPagination" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM shop_subcribe
        ORDER BY id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计总记录数 -->
    <select id="countTotal" resultType="long">
        SELECT COUNT(*) FROM shop_subcribe
    </select>

    <!-- 根据条件统计记录数 -->
    <select id="countByConditions" resultType="long">
        SELECT COUNT(*) FROM shop_subcribe
        <where>
            <if test="shopId != null">AND shop_id = #{shopId}</if>
            <if test="shopType != null">AND shop_type = #{shopType}</if>
            <if test="status != null">AND status = #{status}</if>
            <if test="orderStatus != null">AND order_status = #{orderStatus}</if>
        </where>
    </select>

</mapper>
