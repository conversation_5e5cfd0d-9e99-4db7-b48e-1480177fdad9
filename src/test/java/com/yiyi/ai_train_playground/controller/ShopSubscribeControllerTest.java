package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.ShopSubscribeRequest;
import com.yiyi.ai_train_playground.dto.ShopSubscribeResponse;
import com.yiyi.ai_train_playground.service.ShopSubscribeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 店铺订购控制器测试
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@WebMvcTest(ShopSubscribeController.class)
@DisplayName("店铺订购控制器测试")
public class ShopSubscribeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ShopSubscribeService shopSubscribeService;

    @Test
    @DisplayName("测试创建店铺订购记录 - 成功")
    public void testCreate_Success() throws Exception {
        // 准备测试数据
        ShopSubscribeRequest request = new ShopSubscribeRequest();
        request.setShopId(12345L);
        request.setShopType(0);
        request.setStartTime(LocalDateTime.now());
        request.setEndTime(LocalDateTime.now().plusDays(180));
        request.setStatus(1);
        request.setVersion("不限账号");
        request.setOrderCycle(180);
        request.setBuyer("buyer123");
        request.setNickName("买家昵称");
        request.setServiceName("京东服务");
        request.setItemCode("JD001");
        request.setOrderStatus(4);

        // Mock服务层响应
        when(shopSubscribeService.create(any())).thenReturn(true);

        // 执行请求
        mockMvc.perform(post("/api/shop-subscribe")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("创建成功"));

        log.info("✅ 创建店铺订购记录成功测试通过");
    }

    @Test
    @DisplayName("测试根据ID查询店铺订购记录 - 成功")
    public void testFindById_Success() throws Exception {
        // 准备测试数据
        ShopSubscribeResponse mockResponse = ShopSubscribeResponse.builder()
                .id(1L)
                .shopId(12345L)
                .shopType(0)
                .shopTypeName("京东")
                .startTime(LocalDateTime.now())
                .endTime(LocalDateTime.now().plusDays(180))
                .status(1)
                .statusName("订购")
                .version("不限账号")
                .orderCycle(180)
                .buyer("buyer123")
                .nickName("买家昵称")
                .serviceName("京东服务")
                .itemCode("JD001")
                .orderStatus(4)
                .orderStatusName("订单完成")
                .isValid(true)
                .build();

        // Mock服务层响应
        when(shopSubscribeService.findById(1L)).thenReturn(mockResponse);

        // 执行请求
        mockMvc.perform(get("/api/shop-subscribe/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.shopId").value(12345))
                .andExpect(jsonPath("$.data.shopTypeName").value("京东"))
                .andExpect(jsonPath("$.data.statusName").value("订购"))
                .andExpect(jsonPath("$.data.orderStatusName").value("订单完成"))
                .andExpect(jsonPath("$.data.isValid").value(true));

        log.info("✅ 根据ID查询店铺订购记录成功测试通过");
    }

    @Test
    @DisplayName("测试根据ID查询店铺订购记录 - 记录不存在")
    public void testFindById_NotFound() throws Exception {
        // Mock服务层响应
        when(shopSubscribeService.findById(999L)).thenReturn(null);

        // 执行请求
        mockMvc.perform(get("/api/shop-subscribe/999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("未找到对应的订购记录"));

        log.info("✅ 记录不存在测试通过");
    }

    @Test
    @DisplayName("测试根据店铺ID查询订购记录 - 成功")
    public void testFindByShopId_Success() throws Exception {
        // 准备测试数据
        List<ShopSubscribeResponse> mockResponses = Arrays.asList(
                ShopSubscribeResponse.builder()
                        .id(1L)
                        .shopId(12345L)
                        .shopType(0)
                        .shopTypeName("京东")
                        .status(1)
                        .statusName("订购")
                        .isValid(true)
                        .build(),
                ShopSubscribeResponse.builder()
                        .id(2L)
                        .shopId(12345L)
                        .shopType(0)
                        .shopTypeName("京东")
                        .status(2)
                        .statusName("续费")
                        .isValid(true)
                        .build()
        );

        // Mock服务层响应
        when(shopSubscribeService.findByShopId(12345L)).thenReturn(mockResponses);

        // 执行请求
        mockMvc.perform(get("/api/shop-subscribe/shop/12345")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].shopId").value(12345))
                .andExpect(jsonPath("$.data[1].shopId").value(12345));

        log.info("✅ 根据店铺ID查询订购记录成功测试通过");
    }

    @Test
    @DisplayName("测试删除店铺订购记录 - 成功")
    public void testDeleteById_Success() throws Exception {
        // Mock服务层响应
        when(shopSubscribeService.deleteById(1L)).thenReturn(true);

        // 执行请求
        mockMvc.perform(delete("/api/shop-subscribe/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("删除成功"));

        log.info("✅ 删除店铺订购记录成功测试通过");
    }

    @Test
    @DisplayName("测试更新店铺订购记录 - 成功")
    public void testUpdateById_Success() throws Exception {
        // 准备测试数据
        ShopSubscribeRequest request = new ShopSubscribeRequest();
        request.setShopId(12345L);
        request.setShopType(0);
        request.setStatus(2); // 更新为续费状态
        request.setVersion("高级版");

        // Mock服务层响应
        when(shopSubscribeService.updateById(eq(1L), any())).thenReturn(true);

        // 执行请求
        mockMvc.perform(put("/api/shop-subscribe/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("更新成功"));

        log.info("✅ 更新店铺订购记录成功测试通过");
    }

    @Test
    @DisplayName("测试查询有效的订购记录 - 成功")
    public void testFindValidSubscriptions_Success() throws Exception {
        // 准备测试数据
        List<ShopSubscribeResponse> mockResponses = Arrays.asList(
                ShopSubscribeResponse.builder()
                        .id(1L)
                        .shopId(12345L)
                        .isValid(true)
                        .build()
        );

        // Mock服务层响应
        when(shopSubscribeService.findValidSubscriptions()).thenReturn(mockResponses);

        // 执行请求
        mockMvc.perform(get("/api/shop-subscribe/valid")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].isValid").value(true));

        log.info("✅ 查询有效的订购记录成功测试通过");
    }
}
