package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.controller.jd.JdCallbackController;
import com.yiyi.ai_train_playground.controller.jd.JdCallbackControllerV2;
import com.yiyi.ai_train_playground.dto.JdCallbackResult;
import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;
import com.yiyi.ai_train_playground.mapper.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.service.jd.JdCallbackService;
import com.yiyi.ai_train_playground.service.jd.JdCallbackServiceV2;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 京东回调接口单元测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@ExtendWith(MockitoExtension.class)
class JdCallbackControllerTest {

    @Mock
    private TrainJdAccessTokenMapper trainJdAccessTokenMapper;

    @Mock
    private WebClient.Builder webClientBuilder;

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @Mock
    private JdConfig jdConfig;

    @Mock
    private JwtUtil jwtUtil;

    @Mock
    private HttpServletRequest request;

    @Mock
    private JdCallbackServiceV2 jdCallbackServiceV2;

    @InjectMocks
    private JdCallbackController jdCallbackController;

    @InjectMocks
    private JdCallbackControllerV2 jdCallbackControllerV2;

    @BeforeEach
    void setUp() {
        // Mock JdConfig - 使用lenient模式
        lenient().when(jdConfig.getExpectedState()).thenReturn("YyJdPlayground2025");
        lenient().when(jdConfig.getTokenUrl()).thenReturn("https://oauth.jd.com/oauth/token");
        lenient().when(jdConfig.getAppKey()).thenReturn("test_app_key");
        lenient().when(jdConfig.getAppSecret()).thenReturn("test_app_secret");
        lenient().when(jdConfig.getRedirectBaseUrl()).thenReturn("https://example.com/callback");

        // Mock WebClient chain - 使用lenient模式
        lenient().when(webClientBuilder.build()).thenReturn(webClient);
        lenient().when(webClient.get()).thenReturn(requestHeadersUriSpec);
        lenient().when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        lenient().when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
    }

    @Test
    void testHandleJdCallback_Success() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";
        
        // Mock JWT token extraction
        when(request.getHeader("Authorization")).thenReturn("Bearer test_token");
        when(jwtUtil.getTeamIdFromToken("test_token")).thenReturn(1L);
        when(jwtUtil.getUserIdFromToken("test_token")).thenReturn(100L);
        when(jwtUtil.getUsernameFromToken("test_token")).thenReturn("testuser");

        // Mock 京东API响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("access_token", "test_access_token");
        tokenResponse.put("expires_in", 7200);
        tokenResponse.put("refresh_token", "test_refresh_token");
        tokenResponse.put("scope", "read");
        tokenResponse.put("xid", "test_xid");

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));
        when(trainJdAccessTokenMapper.findByXid("test_xid")).thenReturn(null);
        when(trainJdAccessTokenMapper.insert(any(TrainJdAccessToken.class))).thenReturn(1);

        // 执行测试
        String result = jdCallbackController.handleJdCallback(state, code, request);

        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=1&is_sync_complete=1", result);
        
        // 验证mapper调用
        verify(trainJdAccessTokenMapper).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleJdCallback_IllegalState() {
        // 测试非法state
        String result = jdCallbackController.handleJdCallback("illegal_state", "test_code", request);
        
        assertEquals("redirect:https://example.com/callback?isAuthorize=0&is_sync_complete=0", result);
        
        // 验证没有调用mapper
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleJdCallback_NetworkError() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";
        
        // Mock JWT token extraction
        when(request.getHeader("Authorization")).thenReturn("Bearer test_token");
        when(jwtUtil.getTeamIdFromToken("test_token")).thenReturn(1L);
        when(jwtUtil.getUserIdFromToken("test_token")).thenReturn(100L);
        when(jwtUtil.getUsernameFromToken("test_token")).thenReturn("testuser");

        // Mock 网络错误
        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.error(new RuntimeException("Network error")));

        // 执行测试
        String result = jdCallbackController.handleJdCallback(state, code, request);

        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=0&is_sync_complete=0", result);
        
        // 验证没有调用mapper
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleJdCallback_EmptyResponse() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";
        
        // Mock JWT token extraction
        when(request.getHeader("Authorization")).thenReturn("Bearer test_token");
        when(jwtUtil.getTeamIdFromToken("test_token")).thenReturn(1L);
        when(jwtUtil.getUserIdFromToken("test_token")).thenReturn(100L);
        when(jwtUtil.getUsernameFromToken("test_token")).thenReturn("testuser");

        // Mock 空响应 - 使用Mono.empty()而不是Mono.just(null)
        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.empty());

        // 执行测试
        String result = jdCallbackController.handleJdCallback(state, code, request);

        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=0&is_sync_complete=0", result);
        
        // 验证没有调用mapper
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleJdCallback_MissingRequiredFields() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";
        
        // Mock JWT token extraction
        when(request.getHeader("Authorization")).thenReturn("Bearer test_token");
        when(jwtUtil.getTeamIdFromToken("test_token")).thenReturn(1L);
        when(jwtUtil.getUserIdFromToken("test_token")).thenReturn(100L);
        when(jwtUtil.getUsernameFromToken("test_token")).thenReturn("testuser");

        // Mock 缺少必要字段的响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("expires_in", 7200);
        // 缺少 access_token 和 xid

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));

        // 执行测试
        String result = jdCallbackController.handleJdCallback(state, code, request);

        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=0&is_sync_complete=0", result);
        
        // 验证没有调用mapper
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleJdCallback_UpdateExistingToken() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";
        
        // Mock JWT token extraction
        when(request.getHeader("Authorization")).thenReturn("Bearer test_token");
        when(jwtUtil.getTeamIdFromToken("test_token")).thenReturn(1L);
        when(jwtUtil.getUserIdFromToken("test_token")).thenReturn(100L);
        when(jwtUtil.getUsernameFromToken("test_token")).thenReturn("testuser");

        // Mock 京东API响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("access_token", "test_access_token");
        tokenResponse.put("expires_in", 7200);
        tokenResponse.put("refresh_token", "test_refresh_token");
        tokenResponse.put("scope", "read");
        tokenResponse.put("xid", "test_xid");

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));
        
        // Mock 已存在的token
        TrainJdAccessToken existingToken = new TrainJdAccessToken();
        existingToken.setXid("test_xid");
        when(trainJdAccessTokenMapper.findByXid("test_xid")).thenReturn(existingToken);
        when(trainJdAccessTokenMapper.updateByXid(any(TrainJdAccessToken.class))).thenReturn(1);

        // 执行测试
        String result = jdCallbackController.handleJdCallback(state, code, request);

        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=1&is_sync_complete=1", result);
        
        // 验证调用了update而不是insert
        verify(trainJdAccessTokenMapper).updateByXid(any(TrainJdAccessToken.class));
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleJdCallback_NoJwtToken() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";
        
        // Mock 没有JWT token
        when(request.getHeader("Authorization")).thenReturn(null);
        when(request.getParameter("token")).thenReturn(null);

        // Mock 京东API响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("access_token", "test_access_token");
        tokenResponse.put("expires_in", 7200);
        tokenResponse.put("refresh_token", "test_refresh_token");
        tokenResponse.put("scope", "read");
        tokenResponse.put("xid", "test_xid");

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));
        when(trainJdAccessTokenMapper.findByXid("test_xid")).thenReturn(null);
        when(trainJdAccessTokenMapper.insert(any(TrainJdAccessToken.class))).thenReturn(1);

        // 执行测试
        String result = jdCallbackController.handleJdCallback(state, code, request);

        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=1&is_sync_complete=1", result);
        
        // 验证mapper调用
        verify(trainJdAccessTokenMapper).insert(any(TrainJdAccessToken.class));
    }

    // ==================== JdCallbackControllerV2 测试方法 ====================

    @Test
    void testHandleJdCallbackV2_Success() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";

        // Mock JWT token extraction
        when(request.getHeader("Authorization")).thenReturn("Bearer test_token");
        when(jwtUtil.getTeamIdFromToken("test_token")).thenReturn(1L);
        when(jwtUtil.getUserIdFromToken("test_token")).thenReturn(100L);
        when(jwtUtil.getUsernameFromToken("test_token")).thenReturn("testuser");

        // Mock JdCallbackServiceV2 response
        JdCallbackResult mockResult = JdCallbackResult.success(true, true, "test_access_token");
        when(jdCallbackServiceV2.handleCallback(any())).thenReturn(mockResult);

        // 执行测试
       // String result = jdCallbackControllerV2.handleJdCallback(state, code, request);
        String result = "haha";

        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=1&is_sync_complete=1", result);

        // 验证service调用
        verify(jdCallbackServiceV2).handleCallback(any());
    }

    @Test
    void testHandleJdCallbackV2_ServiceFailure() {
        // 准备测试数据
        String state = "YyJdPlayground2025";
        String code = "test_code";

        // Mock JWT token extraction
        when(request.getHeader("Authorization")).thenReturn("Bearer test_token");
        when(jwtUtil.getTeamIdFromToken("test_token")).thenReturn(1L);
        when(jwtUtil.getUserIdFromToken("test_token")).thenReturn(100L);
        when(jwtUtil.getUsernameFromToken("test_token")).thenReturn("testuser");

        // Mock JdCallbackServiceV2 failure response
        JdCallbackResult mockResult = JdCallbackResult.failure("Service error");
        when(jdCallbackServiceV2.handleCallback(any())).thenReturn(mockResult);

        // 执行测试
      //  String result = jdCallbackControllerV2.handleJdCallback(state, code, request);
        String result = "hahah";
        // 验证结果
        assertEquals("redirect:https://example.com/callback?isAuthorize=0&is_sync_complete=0", result);

        // 验证service调用
        verify(jdCallbackServiceV2).handleCallback(any());
    }
}