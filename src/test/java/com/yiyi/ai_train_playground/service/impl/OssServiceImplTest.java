package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.AiPgGuide;
import com.yiyi.ai_train_playground.service.OssService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

/**
 * OssServiceImpl测试类
 * 用于测试将本地ZIP文件上传到阿里云OSS并获取永久下载链接
 */
@SpringBootTest(classes = AiPgGuide.class)
public class OssServiceImplTest {

    @Autowired
    private OssService ossService;

    private static final String ZIP_FILE_PATH = "C:\\Users\\<USER>\\Downloads\\Compressed\\data-for-1.7.5.zip";
    private static final Long TEAM_ID = 999L;

    /**
     * 测试上传ZIP文件到阿里云OSS并获取永久下载链接
     * 此测试会将指定的ZIP文件上传到临时目录，然后转移到永久目录，最终输出一个永不过期的HTTP下载地址
     * https://ai-playground.oss-cn-shanghai.aliyuncs.com/proc/pic_999_1750524793138_data-for-1.7.5.zip
     */
    @Test
    public void testUploadZipFileAndGetPermanentUrl() throws IOException {
        System.out.println("开始上传文件测试...");

        // 1. 读取本地ZIP文件
        File zipFile = new File(ZIP_FILE_PATH);
        if (!zipFile.exists() || !zipFile.isFile()) {
            throw new IOException("文件不存在: " + ZIP_FILE_PATH);
        }

        System.out.println("1. 读取文件: " + zipFile.getName() + " (" + (zipFile.length() / 1024) + " KB)");

        // 2. 将文件转换为MultipartFile格式
        try (FileInputStream inputStream = new FileInputStream(zipFile)) {
            String fileName = zipFile.getName();
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    "application/zip",
                    inputStream
            );

            System.out.println("2. 上传文件到临时目录...");

            // 3. 上传文件到临时目录（使用PIC类型）
            Integer fileType = 1; // 使用PIC类型
            String tempUrl = ossService.upload(multipartFile, fileType, TEAM_ID);
            System.out.println("3. 临时URL: " + tempUrl);

            // 4. 从URL中提取文件名
            String extractedFileName = tempUrl.substring(tempUrl.lastIndexOf("/") + 1);
            System.out.println("4. 提取的文件名: " + extractedFileName);

            // 5. 生成永久链接
            System.out.println("5. 生成永久链接...");
            List<String> procUrls = ossService.geneProc(new String[]{extractedFileName});

            // 6. 输出永久下载链接
            if (procUrls != null && !procUrls.isEmpty()) {
                String downloadUrl = procUrls.get(0);
                System.out.println("==================================================");
                System.out.println("上传成功！");
                System.out.println("永久下载链接: " + downloadUrl);
                System.out.println("==================================================");
            } else {
                System.err.println("生成永久链接失败");
            }
        }
    }
} 